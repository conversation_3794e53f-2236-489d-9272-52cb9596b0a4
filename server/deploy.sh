#!/bin/bash

# 部署脚本 - 用于在服务器上部署Go UDP服务器
# 服务器部署路径: /root/bnszs

set -e

# 配置变量
DEPLOY_PATH="/root/bnszs"
SERVICE_NAME="bnszs-server"
BINARY_NAME="server"
CONFIG_DIR="config"

echo "开始部署 BNS ZS 服务器..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "错误: 请使用root用户运行此脚本"
    exit 1
fi

# 创建部署目录
echo "创建部署目录: $DEPLOY_PATH"
mkdir -p $DEPLOY_PATH
mkdir -p $DEPLOY_PATH/$CONFIG_DIR

# 停止现有服务（如果存在）
echo "停止现有服务..."
systemctl stop $SERVICE_NAME 2>/dev/null || true

# 复制二进制文件
echo "复制二进制文件..."
if [ -f "./bin/$BINARY_NAME" ]; then
    echo "找到二进制文件: ./bin/$BINARY_NAME"
    cp "./bin/$BINARY_NAME" "$DEPLOY_PATH/"
    chmod +x "$DEPLOY_PATH/$BINARY_NAME"
elif [ -f "./$BINARY_NAME" ]; then
    echo "找到二进制文件: ./$BINARY_NAME"
    cp "./$BINARY_NAME" "$DEPLOY_PATH/"
    chmod +x "$DEPLOY_PATH/$BINARY_NAME"
elif [ -f "./main" ]; then
    echo "找到二进制文件: ./main"
    cp "./main" "$DEPLOY_PATH/$BINARY_NAME"
    chmod +x "$DEPLOY_PATH/$BINARY_NAME"
else
    echo "错误: 找不到编译后的二进制文件"
    echo "当前目录: $(pwd)"
    echo "目录内容:"
    ls -la
    echo "请确保以下文件之一存在:"
    echo "  - ./bin/server"
    echo "  - ./server"
    echo "  - ./main"
    exit 1
fi

# 验证复制结果
if [ -f "$DEPLOY_PATH/$BINARY_NAME" ]; then
    echo "✅ 二进制文件复制成功: $DEPLOY_PATH/$BINARY_NAME"
    ls -la "$DEPLOY_PATH/$BINARY_NAME"
else
    echo "❌ 二进制文件复制失败"
    exit 1
fi

# 复制配置文件
echo "复制配置文件..."
if [ -f "./$CONFIG_DIR/config.yaml" ]; then
    # 检查源文件和目标文件是否相同
    if [ "$(realpath ./$CONFIG_DIR/config.yaml)" != "$(realpath $DEPLOY_PATH/$CONFIG_DIR/config.yaml)" ]; then
        cp "./$CONFIG_DIR/config.yaml" "$DEPLOY_PATH/$CONFIG_DIR/"
        echo "✅ 配置文件复制成功"
    else
        echo "ℹ️ 配置文件已在目标位置，跳过复制"
    fi
else
    echo "⚠️ 配置文件不存在，服务器将使用默认配置"
fi

# 创建systemd服务文件
echo "创建systemd服务文件..."
cat > /etc/systemd/system/$SERVICE_NAME.service << EOF
[Unit]
Description=BNS ZS UDP Server
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$DEPLOY_PATH
ExecStart=$DEPLOY_PATH/$BINARY_NAME
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd配置
echo "重新加载systemd配置..."
systemctl daemon-reload

# 启用服务
echo "启用服务..."
systemctl enable $SERVICE_NAME

# 启动服务
echo "启动服务..."
systemctl start $SERVICE_NAME

# 检查服务状态
echo "检查服务状态..."
sleep 2
if systemctl is-active --quiet $SERVICE_NAME; then
    echo "✅ 服务部署成功！"
    echo "服务状态:"
    systemctl status $SERVICE_NAME --no-pager -l
    echo ""
    echo "查看日志: journalctl -u $SERVICE_NAME -f"
    echo "停止服务: systemctl stop $SERVICE_NAME"
    echo "重启服务: systemctl restart $SERVICE_NAME"
else
    echo "❌ 服务启动失败！"
    echo "查看错误日志:"
    journalctl -u $SERVICE_NAME --no-pager -l
    exit 1
fi

echo "部署完成！"
