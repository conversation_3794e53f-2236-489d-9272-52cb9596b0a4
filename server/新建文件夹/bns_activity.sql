-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-08-02 12:06:27
-- 服务器版本： 5.7.44-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `bns`
--

-- --------------------------------------------------------

--
-- 表的结构 `bns_activity`
--

CREATE TABLE `bns_activity` (
  `activity_id` bigint(20) UNSIGNED NOT NULL,
  `activity_name` varchar(200) NOT NULL,
  `activity_url` varchar(200) DEFAULT NULL,
  `service_type` tinyint(4) NOT NULL DEFAULT '0',
  `priority` tinyint(4) NOT NULL DEFAULT '0',
  `version` smallint(6) NOT NULL DEFAULT '1',
  `status` tinyint(4) NOT NULL DEFAULT '0',
  `begin_time` datetime(3) NOT NULL,
  `end_time` datetime(3) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- 转存表中的数据 `bns_activity`
--

INSERT INTO `bns_activity` (`activity_id`, `activity_name`, `activity_url`, `service_type`, `priority`, `version`, `status`, `begin_time`, `end_time`) VALUES
(632706, '怀旧服cdk兑换', '', 2, 5, 3, 1, '2024-04-25 08:00:00.000', '2030-10-30 07:59:00.000'),
(688155, '手机号绑定集合', 'https://bns.qq.com/cp/neo/a20241211bnshmlz/index.html', 0, 5, 1, 1, '2025-06-25 08:00:00.000', '2025-12-24 07:59:59.000'),
(688156, '手机号绑定集合', 'https://bns.qq.com/cp/neo/a20241211bnshmlz/index.html', 1, 5, 1, 1, '2025-06-25 08:00:00.000', '2025-12-24 07:59:59.000'),
(688157, '手机号绑定集合', 'https://bns.qq.com/cp/neo/a20241211bnshmlz/index.html', 2, 5, 1, 1, '2025-06-25 08:00:00.000', '2025-12-24 07:59:59.000'),
(721136, '巅峰再会 璀璨回馈', 'https://bns.qq.com/neo/a20250512dfzh/index.html', 2, 5, 3, 1, '2025-06-24 08:00:00.000', '2025-12-24 07:59:00.000'),
(738361, '巅峰相会 感恩纪念', 'https://bns.qq.com/neo/a20250610gnjn', 2, 5, 7, 1, '2025-06-25 08:00:00.000', '2025-08-25 07:59:00.000'),
(745071, '剑灵怀旧服-人拉人', 'https://bns.qq.com/cp/a20250709hljs/index.html', 1, 5, 5, 1, '2025-07-09 08:00:00.000', '2025-09-25 07:59:00.000');

--
-- 转储表的索引
--

--
-- 表的索引 `bns_activity`
--
ALTER TABLE `bns_activity`
  ADD PRIMARY KEY (`activity_id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `bns_activity`
--
ALTER TABLE `bns_activity`
  MODIFY `activity_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=745074;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
