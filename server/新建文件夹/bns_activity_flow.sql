-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-08-02 12:06:30
-- 服务器版本： 5.7.44-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `bns`
--

-- --------------------------------------------------------

--
-- 表的结构 `bns_activity_flow`
--

CREATE TABLE `bns_activity_flow` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `activity_id` bigint(20) UNSIGNED NOT NULL,
  `flow_id` bigint(20) UNSIGNED NOT NULL,
  `flow_name` varchar(200) NOT NULL,
  `group` int(10) UNSIGNED DEFAULT NULL,
  `ide_token` varchar(100) NOT NULL,
  `tpl_type` varchar(100) DEFAULT 'default',
  `parameters` text,
  `status` tinyint(4) NOT NULL DEFAULT '1',
  `sort_order` bigint(20) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- 转存表中的数据 `bns_activity_flow`
--

INSERT INTO `bns_activity_flow` (`id`, `activity_id`, `flow_id`, `flow_name`, `group`, `ide_token`, `tpl_type`, `parameters`, `status`, `sort_order`) VALUES
(1, 745071, 422156, '查询绑定', NULL, 'tA0PiQ', 'bindarea#query_map_id#', '{}', 0, 1),
(2, 745071, 422157, '提交绑定', NULL, 'CkBs1l', 'bindarea#bind_map_id#', '{}', 1, 2),
(3, 745071, 422133, '发起赠送照片', NULL, 'Z0FnZg', 'default', '{\"iCardId\":{\"desc\":\"卡牌ID\",\"value\":\"\"}}', 0, 3),
(4, 745071, 422135, '发起交换照片', NULL, 'xMIeh2', 'default', '{\"iGiveCardId\":{\"desc\":\"赠送卡牌ID\",\"value\":\"\"},\"iNeedCardId\":{\"desc\":\"需要的卡牌ID\",\"value\":\"\"}}', 0, 4),
(5, 745071, 422136, '同意交换照片', NULL, 'bn9MoN', 'default', '{\"sExchangeKey\":{\"desc\":\"交换key\",\"value\":\"\"}}', 0, 5),
(6, 745071, 422138, '每日消耗60点疲劳得3卡牌', NULL, 'Y10nPL', 'default', '{}', 1, 6),
(7, 745071, 422154, '【525】本周收集排名', NULL, 'm4ezqG', 'default', '{\"page\":{\"desc\":\"当前页码\",\"value\":\"\"}}', 0, 7),
(8, 745071, 423404, '每日完成1次箱子任务得3卡牌', NULL, 'CrVUWo', 'default', '{}', 1, 8),
(9, 745071, 423475, '活动完成1次剧情任务得9卡牌', NULL, 'PTTB6q', 'default', '{}', 1, 9),
(10, 745071, 424150, '查询领取记录', NULL, 'zkFHlS', 'mrms#history#', '{\"iPageNow\":{\"desc\":\"\",\"value\":\"\"},\"iPageSize\":{\"desc\":\"\",\"value\":\"\"}}', 0, 10),
(11, 745071, 422132, '初始化', NULL, 'Gor777', 'default', '{\"sExchangeKey\":{\"desc\":\"交换key\",\"value\":\"\"},\"sInviteKey\":{\"desc\":\"邀请码\",\"value\":\"\"},\"sNickname\":{\"desc\":\"昵称\",\"value\":\"\"},\"sSendKey\":{\"desc\":\"赠送key\",\"value\":\"\"}}', 0, 11),
(12, 745071, 422134, '收下赠送照片', NULL, 'nhWCHn', 'default', '{\"sSendKey\":{\"desc\":\"赠送key\",\"value\":\"\"}}', 0, 12),
(13, 745071, 422139, '集齐洪门之众', NULL, 'kyp701', 'default', '{\"id\":{\"desc\":\"集齐ID\",\"value\":\"1\"}}', 1, 13),
(14, 745071, 422144, '发起交换照片检查', NULL, 'hDOKcU', 'default', '{\"iGiveCardId\":{\"desc\":\"交换卡牌ID\",\"value\":\"\"}}', 0, 14),
(15, 745071, 422147, '银汉转玉盘（三档）', NULL, 'QSEGKO', 'default', '{}', 1, 15),
(16, 745071, 422140, '集换记录', NULL, 'nHpRlC', 'default', '{\"page\":{\"desc\":\"当前页码\",\"value\":\"\"}}', 0, 16),
(17, 745071, 422148, '每周前10名领奖', NULL, 'bSWWUY', 'default', '{\"type\":{\"desc\":\"领取名次\",\"value\":\"\"}}', 0, 17),
(18, 745071, 422155, '【10】本周排行前十名单', NULL, 'U20i29', 'default', '{}', 0, 18),
(19, 745071, 422149, '客态接受邀请', NULL, 'KyGE2o', 'default', '{\"sInviteKey\":{\"desc\":\"邀请码\",\"value\":\"\"}}', 0, 19),
(20, 745071, 422150, '邀请新进/回流得3卡牌', NULL, 'jP8xWo', 'default', '{}', 1, 20),
(21, 745071, 422151, '新进/回流玩家得6卡牌', NULL, 'LDFRPK', 'default', '{}', 1, 21),
(22, 745071, 422139, '集齐俞家村', NULL, 'kyp701', 'default', '{\"id\":{\"desc\":\"集齐ID\",\"value\":\"2\"}}', 1, 13),
(23, 745071, 422139, '集齐破天城都', NULL, 'kyp701', 'default', '{\"id\":{\"desc\":\"集齐ID\",\"value\":\"3\"}}', 1, 13),
(24, 745071, 422139, '集齐黑龙教首', NULL, 'kyp701', 'default', '{\"id\":{\"desc\":\"集齐ID\",\"value\":\"4\"}}', 1, 13),
(25, 745071, 422139, '集齐螳螂人', NULL, 'kyp701', 'default', '{\"id\":{\"desc\":\"集齐ID\",\"value\":\"5\"}}', 1, 13),
(26, 738361, 411808, '查询绑定', NULL, '3WPCzt', 'bindarea#query_map_id#', '{}', 0, 2),
(27, 738361, 411809, '提交绑定', NULL, 'DInI3o', 'bindarea#bind_map_id#', '{}', 1, 3),
(28, 738361, 411750, '初始化', NULL, '61RO1o', 'default', '{}', 0, 1),
(29, 738361, 411759, '领取注册好礼', NULL, 'FbAh8b', 'default', '{}', 1, 4),
(30, 738361, 411761, '领取第一天任务奖励', NULL, 'VZrZmi', 'default', '{\"iTargetDay\":{\"desc\":\"哪一天任务\",\"value\":\"0\"}}', 1, 5),
(31, 738361, 411762, '领取里程碑奖励', NULL, 'uuRvVP', 'default', '{}', 1, 6),
(32, 738361, 411812, '领取满级礼', NULL, 'HEfsCR', 'default', '{}', 1, 7),
(33, 738361, 413228, '查询某一天任务资格', NULL, 'XGbhlK', 'default', '{\"iTargetDay\":{\"desc\":\"某一天\",\"value\":\"\"}}', 0, 8),
(34, 738361, 416003, '查询领取记录', NULL, 'FSjRqP', 'mrms#history#', '{\"iPageNow\":{\"desc\":\"\",\"value\":\"\"},\"iPageSize\":{\"desc\":\"\",\"value\":\"\"}}', 0, 9),
(35, 738361, 411761, '领取第二天任务奖励', NULL, 'VZrZmi', 'default', '{\"iTargetDay\":{\"desc\":\"哪一天任务\",\"value\":\"1\"}}', 1, 5),
(36, 738361, 411761, '领取第三天任务奖励', NULL, 'VZrZmi', 'default', '{\"iTargetDay\":{\"desc\":\"哪一天任务\",\"value\":\"2\"}}', 1, 5),
(37, 738361, 411761, '领取第四天任务奖励', NULL, 'VZrZmi', 'default', '{\"iTargetDay\":{\"desc\":\"哪一天任务\",\"value\":\"3\"}}', 1, 5),
(38, 738361, 411761, '领取第五天任务奖励', NULL, 'VZrZmi', 'default', '{\"iTargetDay\":{\"desc\":\"哪一天任务\",\"value\":\"4\"}}', 1, 5),
(39, 738361, 411761, '领取第六天任务奖励', NULL, 'VZrZmi', 'default', '{\"iTargetDay\":{\"desc\":\"哪一天任务\",\"value\":\"5\"}}', 1, 5),
(40, 738361, 411761, '领取第七天任务奖励', NULL, 'VZrZmi', 'default', '{\"iTargetDay\":{\"desc\":\"哪一天任务\",\"value\":\"6\"}}', 1, 5),
(41, 721136, 415003, '查询绑定', NULL, 'MkV8FQ', 'bindarea#query_map_id#', '{}', 0, 1),
(42, 721136, 415004, '提交绑定', NULL, 'wW1ytx', 'bindarea#bind_map_id#', '{}', 1, 2),
(43, 721136, 415002, '领取抽奖记录游戏道具', NULL, 'NKzOSB', 'default', '{\"iId\":{\"desc\":\"礼包自增ID\",\"value\":\"\"},\"iPackageId\":{\"desc\":\"礼包id\",\"value\":\"\"}}', 0, 3),
(44, 721136, 416197, '评估接口（无预约、无记录邀请）', NULL, 'mkVua7', 'default', '{}', 0, 4),
(45, 721136, 400001, '初始化加载', NULL, 'MiZGhQ', 'default', '{}', 0, 5),
(46, 721136, 415030, '领取返利箱子1', NULL, 'JHCnMs', 'default', '{\"type\":{\"desc\":\"领取第x周箱子\",\"value\":\"1\"}}', 1, 6),
(47, 721136, 415084, '开通战令领取额外奖励', NULL, 'gUVu5S', 'default', '{}', 1, 7),
(48, 721136, 415085, '初始化-渲染宝箱数据', NULL, 'W1AhBF', 'default', '{}', 0, 8),
(49, 721136, 400035, '评估接口', NULL, 'Rn4klS', 'default', '{\"inviteCode\":{\"desc\":\"加密邀请码\",\"value\":\"\"}}', 0, 9),
(50, 721136, 404065, '抽奖', NULL, 'h0XIb8', 'default', '{}', 1, 10),
(51, 721136, 405540, '查询领取记录', NULL, '8FGdm4', 'mrms#history#', '{\"iPageNow\":{\"desc\":\"\",\"value\":\"\"},\"iPageSize\":{\"desc\":\"\",\"value\":\"\"}}', 0, 11),
(52, 721136, 415030, '领取返利箱子2', NULL, 'JHCnMs', 'default', '{\"type\":{\"desc\":\"领取第x周箱子\",\"value\":\"2\"}}', 1, 6),
(53, 721136, 415030, '领取返利箱子3', NULL, 'JHCnMs', 'default', '{\"type\":{\"desc\":\"领取第x周箱子\",\"value\":\"3\"}}', 1, 6),
(54, 721136, 415030, '领取返利箱子4', NULL, 'JHCnMs', 'default', '{\"type\":{\"desc\":\"领取第x周箱子\",\"value\":\"4\"}}', 1, 6),
(55, 721136, 415030, '领取返利箱子5', NULL, 'JHCnMs', 'default', '{\"type\":{\"desc\":\"领取第x周箱子\",\"value\":\"5\"}}', 1, 6),
(56, 721136, 415030, '领取返利箱子6', NULL, 'JHCnMs', 'default', '{\"type\":{\"desc\":\"领取第x周箱子\",\"value\":\"6\"}}', 1, 6),
(57, 688157, 413949, '绑定大区-巅峰服', NULL, 'Xcsr2b', 'bindarea#bind_map_id#', '{}', 1, 1),
(58, 688155, 354892, '剑灵提交绑定', NULL, '5OzXiP', 'bindarea#bind_map_id#', '{}', 1, 1),
(59, 688156, 354904, '剑灵怀旧提交绑定', NULL, 'Dd0IL1', 'bindarea#bind_map_id#', '{}', 1, 3),
(60, 688155, 356929, '取消授权-qq', NULL, 'NTFOsq', 'default', '{}', 1, 2),
(61, 688155, 356968, '取消授权-微信', NULL, 'YgU2kf', 'default', '{}', 1, 3),
(62, 688157, 413911, '剑灵巅峰-每月领取', NULL, 'ASYZkY', 'default', '{}', 1, 6),
(63, 688155, 356069, '单业务-发送手机验证码', NULL, 'aqWLzE', 'default', '{\"mobile\":{\"desc\":\"手机号校验\",\"value\":\"\"},\"type\":{\"desc\":\"4-剑灵怀旧\",\"value\":\"\"}}', 1, 4),
(64, 688155, 356435, '剑灵-新用户领取', NULL, '2Rim7b', 'default', '{}', 1, 10),
(65, 688155, 356225, '单业务-手机授权', NULL, 'TZVPne', 'default', '{\"code\":{\"desc\":\"验证码\",\"value\":\"\"},\"mobile\":{\"desc\":\"\",\"value\":\"\"},\"type\":{\"desc\":\"4-怀旧\",\"value\":\"\"}}', 1, 5),
(66, 688155, 356482, '剑灵-初始化', NULL, 'GUx0hz', 'default', '{}', 1, 6),
(67, 688155, 356930, '双业务授权', NULL, '6uLTQ0', 'default', '{\"code\":{\"desc\":\"验证码\",\"value\":\"\"},\"mobile\":{\"desc\":\"\",\"value\":\"\"}}', 1, 7),
(68, 688155, 419704, '【3.0接口】发送取消解绑验证码', NULL, 'V7NHih', 'default', '{}', 1, 8),
(69, 688156, 356483, '剑灵怀旧-初始化', NULL, '7AxntP', 'default', '{}', 1, 13),
(70, 688155, 421043, '【3.0接口】手机验证码解绑', NULL, 'IMIhGC', 'default', '{\"code\":{\"desc\":\"验证码\",\"value\":\"\"}}', 1, 9),
(71, 688155, 356446, '剑灵-每月领取', NULL, '5bOv08', 'default', '{}', 1, 11),
(72, 688155, 356454, '剑灵-满4个月领取', NULL, 'UWves0', 'default', '{}', 1, 12),
(73, 688156, 356468, '剑灵怀旧-每月领取', NULL, 'YHzh8P', 'default', '{}', 1, 17),
(74, 632706, 281354, '口令码CDKEY兑换', NULL, 'p1q56T', 'default', '{\"sArea\":{\"desc\":\"\",\"value\":\"\"},\"sCode\":{\"desc\":\"图形验证码\",\"value\":\"\"},\"sPartition\":{\"desc\":\"\",\"value\":\"\"},\"sPassword\":{\"desc\":\"口令码\",\"value\":\"\"},\"sPlatId\":{\"desc\":\"\",\"value\":\"\"},\"sRoleId\":{\"desc\":\"\",\"value\":\"\"},\"sRoleName\":{\"desc\":\"\",\"value\":\"\"},\"sServiceType\":{\"desc\":\"游戏业务编码\",\"value\":\"\"},\"sUrFrom\":{\"desc\":\"礼包领取来源（追加到活动URL后面）\",\"value\":\"\"},\"type\":{\"desc\":\"兑换类型\",\"value\":\"\"}}', 0, 2),
(75, 632706, 282203, '获取cdk', NULL, 'XMUtf0', 'default', '{\"sArea\":{\"desc\":\"\",\"value\":\"\"},\"sPartition\":{\"desc\":\"\",\"value\":\"\"},\"sPlatId\":{\"desc\":\"\",\"value\":\"\"},\"sRoleId\":{\"desc\":\"\",\"value\":\"\"},\"sRoleName\":{\"desc\":\"\",\"value\":\"\"}}', 0, 3),
(76, 632706, 285779, '小圆', 101, '6wdXPC', 'bindarea#bind_map_id#', '{\"sCode\":{\"desc\":\"\",\"value\":\"\"},\"sPassword\":{\"desc\":\"口令码\",\"value\":\"002yuan\"},\"sServiceType\":{\"desc\":\"游戏业务编码\",\"value\":\"neo\"},\"sUrFrom\":{\"desc\":\"礼包领取来源（追加到活动URL后面）\",\"value\":\"\"},\"type\":{\"desc\":\"兑换类型\",\"value\":\"2\"}}', 1, 4),
(77, 632706, 351003, '初始化', NULL, 'r0brCs', 'default', '{}', 0, 1);

--
-- 转储表的索引
--

--
-- 表的索引 `bns_activity_flow`
--
ALTER TABLE `bns_activity_flow`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_bns_activity_flow_activity_id` (`activity_id`),
  ADD KEY `f_group` (`group`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `bns_activity_flow`
--
ALTER TABLE `bns_activity_flow`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=78;

--
-- 限制导出的表
--

--
-- 限制表 `bns_activity_flow`
--
ALTER TABLE `bns_activity_flow`
  ADD CONSTRAINT `f_group` FOREIGN KEY (`group`) REFERENCES `bns_activity_group` (`group`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
