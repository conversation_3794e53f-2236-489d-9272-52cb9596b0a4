-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-08-02 12:06:32
-- 服务器版本： 5.7.44-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `bns`
--

-- --------------------------------------------------------

--
-- 表的结构 `bns_activity_group`
--

CREATE TABLE `bns_activity_group` (
  `group` int(10) UNSIGNED NOT NULL,
  `text` longtext
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- 转存表中的数据 `bns_activity_group`
--

INSERT INTO `bns_activity_group` (`group`, `text`) VALUES
(100, '怀旧服8月礼包码\n请选择自己喜欢的主播支持！'),
(101, '巅峰服8月礼包码\n请选择自己喜欢的主播支持！');

--
-- 转储表的索引
--

--
-- 表的索引 `bns_activity_group`
--
ALTER TABLE `bns_activity_group`
  ADD PRIMARY KEY (`group`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `bns_activity_group`
--
ALTER TABLE `bns_activity_group`
  MODIFY `group` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=101;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
