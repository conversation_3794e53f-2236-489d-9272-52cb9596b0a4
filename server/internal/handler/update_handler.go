package handler

import (
	"fmt"
	"net"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/logger"
)

// 更新配置处理器
type UpdateHandler struct {
	updateService *service.UpdateService
}

// 创建更新配置处理器
func NewUpdateHandler(updateService *service.UpdateService) *UpdateHandler {
	return &UpdateHandler{
		updateService: updateService,
	}
}

// 处理更新配置请求
func (h *UpdateHandler) HandleUpdateConfigRequest(msg *binary.Message, remoteAddr *net.UDPAddr) (*binary.BinaryWriter, error) {
	// 解析更新配置请求
	reader := binary.NewBinaryReader(msg.Body)
	appType, err1 := reader.ReadUint8()  // 读取AppType字段（1字节）
	version, err2 := reader.ReadString() // 读取Version字段（字符串）
	if err1 != nil || err2 != nil {
		return nil, fmt.Errorf("Failed to read update request")
	}

	// App类型是硬编码的，和数据库中的主键无关
	// 更新服务类根据应用名称查询数据表
	var appName string
	switch appType {
	case 0x1:
		appName = "bns-helper"
	case 0x2:
		appName = "bns-preview-tools"
	default:
		logger.Warn("Unknown app type %d in update config request from %s", appType, remoteAddr.IP.String())
		return nil, fmt.Errorf("未知的应用类型")
	}

	// 获取更新配置
	config, update := h.updateService.GetUpdateConfig(appName, version)
	if config == nil {
		logger.Warn("Update configuration not found for app: %s", appName)
		return nil, fmt.Errorf("获取更新配置信息失败")
	}

	// 构建响应
	writer := binary.NewBinaryWriter()
	writer.WriteUint32(binary.ErrorCodeSuccess)
	if update {
		writer.WriteString(config.URL)
		writer.WriteString(config.ExecutablePath)
		writer.WriteString(config.Checksum)
	}
	logger.Info("[Gateway] Update config response sent for app %s to %s", appName, remoteAddr.IP.String())
	return writer, nil
}
