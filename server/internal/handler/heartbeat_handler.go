package handler

import (
	"fmt"
	"udp-server/server/internal/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/logger"
)

// 心跳处理器
type HeartbeatHandler struct {
	authService         *service.AuthService
	heartbeatService    *service.HeartbeatService
	updateService       *service.UpdateService
	permissionService   *service.PermissionService
	announcementService *service.AnnouncementService
}

// 创建新的心跳处理器
func NewHeartbeatHandler(authService *service.AuthService, heartbeatService *service.HeartbeatService, updateService *service.UpdateService, permissionService *service.PermissionService, announcementService *service.AnnouncementService) *HeartbeatHandler {
	return &HeartbeatHandler{
		authService:         authService,
		heartbeatService:    heartbeatService,
		updateService:       updateService,
		permissionService:   permissionService,
		announcementService: announcementService,
	}
}

// 处理心跳请求
func (h *HeartbeatHandler) HandleHeartbeatRequest(msg *binary.Message) (*binary.BinaryWriter, error) {
	// 使用统一验证机制
	reader := binary.NewBinaryReader(msg.Body)
	userInterface, err := reader.ValidateToken(h.authService)
	if err != nil {
		logger.Error("心跳处理失败: %v", err)
		return nil, err
	}

	// 类型转换
	user, ok := userInterface.(*model.User)
	if !ok {
		logger.Error("心跳处理失败，用户类型错误")
		return nil, fmt.Errorf("用户类型错误")
	}

	// 读取版本信息
	version, err := reader.ReadString()
	if err != nil {
		logger.Warn("读取版本信息失败: %v", err)
		version = ""
	}

	// 更新心跳服务中的心跳时间
	deviceID := fmt.Sprintf("%d", user.Uin)
	if h.heartbeatService != nil {
		if err := h.heartbeatService.UpdateHeartbeat(deviceID); err != nil {
			return nil, fmt.Errorf("更新心跳时间失败: %v", err)
		}
	} else {
		logger.Error("心跳服务未初始化")
		return nil, fmt.Errorf("心跳服务未初始化")
	}

	// 构建响应信息
	response := &model.HeartbeatResponse{}

	// 获取当前在线用户数量
	if h.heartbeatService != nil {
		response.OnlineUserCount = h.heartbeatService.GetOnlineUserCount()
	}

	// 检查是否需要强制更新
	if h.updateService != nil && version != "" {
		response.ForceUpdate = h.updateService.CheckUpdate("bns-helper", version)
	}

	// 获取当前公告版本
	if h.announcementService != nil {
		response.AnnouncementVersion = h.announcementService.GetCurrentVersion()
	}

	// 构建响应
	logger.Debug("心跳成功: QQ号=%d", user.Uin)
	return EncodeHeartbeatResponse(response), nil
}

// 创建心跳响应包
func EncodeHeartbeatResponse(resp *model.HeartbeatResponse) *binary.BinaryWriter {
	writer := binary.NewBinaryWriter()
	writer.WriteUint32(resp.ErrorCode)

	// 只有在ErrorCode非0时才包含ErrorMsg
	if resp.ErrorCode != 0 {
		writer.WriteString(resp.ErrorMsg)
	} else {
		writer.WriteUint32(resp.OnlineUserCount)
		writer.WriteBool(resp.ForceUpdate)
		writer.WriteUint16(resp.AnnouncementVersion)
	}

	return writer
}
