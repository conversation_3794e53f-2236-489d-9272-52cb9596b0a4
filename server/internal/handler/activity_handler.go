package handler

import (
	"fmt"
	"net"

	"udp-server/server/internal/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/logger"
)

// 活动处理器
type ActivityHandler struct {
	activityService *service.ActivityService
	authService     *service.AuthService
}

// 创建活动处理器实例
func NewActivityHandler(activityService *service.ActivityService, authService *service.AuthService) *ActivityHandler {
	return &ActivityHandler{
		activityService: activityService,
		authService:     authService,
	}
}

// 处理获取活动版本请求
func (h *ActivityHandler) HandleActivityVersionRequest(msg *binary.Message, remoteAddr *net.UDPAddr) (*binary.BinaryWriter, error) {
	logger.Debug("处理活动版本请求，来自: %s", remoteAddr.IP.String())

	// 使用统一验证机制
	reader := binary.NewBinaryReader(msg.Body)
	_, err := reader.ValidateToken(h.authService)
	if err != nil {
		return nil, err
	}

	// 获取活动版本
	version := h.activityService.GetCurrentVersion()

	// 创建响应
	writer := binary.NewBinaryWriter()
	writer.WriteUint32(binary.ErrorCodeSuccess)
	writer.WriteUint16(version)
	return writer, nil
}

// 处理获取活动列表请求
func (h *ActivityHandler) HandleGetActivityListRequest(msg *binary.Message, remoteAddr *net.UDPAddr) (*binary.BinaryWriter, error) {
	logger.Debug("处理活动列表请求，来自: %s", remoteAddr.IP.String())

	// 使用统一验证机制
	reader := binary.NewBinaryReader(msg.Body)
	_, err := reader.ValidateToken(h.authService)
	if err != nil {
		return nil, err
	}

	// 获取活动列表
	activities, err := h.activityService.GetActivities()
	if err != nil {
		logger.Error("获取活动列表失败: %v", err)
		return nil, err
	}

	// 构建响应
	writer := binary.NewBinaryWriter()
	writer.WriteUint32(binary.ErrorCodeSuccess)
	writer.WriteUint32(uint32(len(activities))) // 活动列表
	for _, item := range activities {
		writer.WriteUint64(item.ActivityId)
		writer.WriteUint16(item.Version)
	}
	return writer, nil
}

// 处理获取活动详情请求
func (h *ActivityHandler) HandleActivityDetailRequest(msg *binary.Message, remoteAddr *net.UDPAddr) (*binary.BinaryWriter, error) {
	logger.Debug("处理活动详情请求，来自: %s", remoteAddr.IP.String())

	// 使用统一验证机制
	reader := binary.NewBinaryReader(msg.Body)
	_, err := reader.ValidateToken(h.authService)
	if err != nil {
		return nil, err
	}

	// 读取ActivityId
	ActivityId, err := reader.ReadUint64()
	if err != nil {
		return nil, fmt.Errorf("failed to read activity id: %w", err)
	}

	// 获取活动详情
	activity, err := h.activityService.GetActivity(ActivityId)
	if err != nil {
		return nil, fmt.Errorf("获取活动详情失败: %v", err)
	}

	// 构建响应
	writer := binary.NewBinaryWriter()
	writer.WriteUint32(binary.ErrorCodeSuccess)
	h.encodeActivityData(writer, activity)
	return writer, nil
}

// 编码活动数据
func (h *ActivityHandler) encodeActivityData(writer *binary.BinaryWriter, activity *model.Activity) {
	logger.Debug("编码活动数据: ID=%d, Name=%s, 流程数=%d", activity.ActivityId, activity.ActivityName, len(activity.Flows))

	writer.WriteUint64(activity.ActivityId)    // ActivityId
	writer.WriteString(activity.ActivityName)  // ActivityName
	writer.WriteString(activity.ActivityUrl)   // ActivityUrl
	writer.WriteUint8(activity.ServiceType)    // ServiceType
	writer.WriteUint16(activity.Version)       // Version
	writer.WriteInt64(activity.EndTime.Unix()) // EndTime

	// 收集所有分组信息（去重）
	groupMap := make(map[uint32]string)
	groupIds := make(map[uint32]bool) // 收集所有非零的分组ID

	for _, flow := range activity.Flows {
		logger.Debug("流程: %s, Group: %d, GroupInfo: %v", flow.FlowName, flow.Group, flow.GroupInfo)
		if flow.GroupInfo != nil {
			groupMap[flow.Group] = flow.GroupInfo.Text
			logger.Debug("添加分组信息: GroupID=%d, GroupName=%s", flow.Group, flow.GroupInfo.Text)
		} else if flow.Group != 0 {
			logger.Warn("流程 %s (Group: %d) 的 GroupInfo 为空，尝试手动查询", flow.FlowName, flow.Group)
			groupIds[flow.Group] = true
		}
	}

	// 如果有分组信息缺失，手动查询
	if len(groupIds) > 0 {
		var groups []model.ActivityGroup
		var groupIdList []uint32
		for groupId := range groupIds {
			groupIdList = append(groupIdList, groupId)
		}

		err := h.activityService.GetDB().Where("group IN ?", groupIdList).Find(&groups).Error
		if err != nil {
			logger.Error("手动查询分组信息失败: %v", err)
		} else {
			for _, group := range groups {
				groupMap[group.Group] = group.Text
				logger.Debug("手动查询到分组信息: GroupID=%d, GroupName=%s", group.Group, group.Text)
			}
		}
	}

	// 编码分组数量和分组信息
	logger.Debug("编码分组信息: 总数=%d", len(groupMap))
	writer.WriteUint16(uint16(len(groupMap)))
	for groupId, groupName := range groupMap {
		logger.Debug("编码分组: ID=%d, Name=%s", groupId, groupName)
		writer.WriteUint32(groupId)   // 分组ID
		writer.WriteString(groupName) // 分组名称
	}

	// 流程数量（数据库查询时进行过滤并排序）
	writer.WriteUint16(uint16(len(activity.Flows)))
	for _, flow := range activity.Flows {
		h.encodeFlowData(writer, &flow)
	}
}

// 编码流程数据
func (h *ActivityHandler) encodeFlowData(writer *binary.BinaryWriter, flow *model.ActivityFlow) {
	writer.WriteUint64(flow.FlowId)
	writer.WriteString(flow.FlowName) // Name
	writer.WriteString(flow.IdeToken) // IdeToken
	writer.WriteString(flow.TplType)  // TplType
	writer.WriteUint32(flow.Group)    // Group

	// 解析参数
	params := flow.GetParameters()
	writer.WriteUint16(uint16(len(params)))
	for _, param := range params {
		writer.WriteString(param.Key)
		writer.WriteString(param.Value)
	}
}
