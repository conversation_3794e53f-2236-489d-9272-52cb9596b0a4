package service

import (
	"sync"
	"time"

	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"
	"udp-server/server/pkg/metrics"
)

// HeartbeatService 心跳服务 - 纯内存实现，专注于实时在线状态管理
type HeartbeatService struct {
	mu            sync.RWMutex
	heartbeats    map[string]time.Time // deviceID -> 最后心跳时间
	timeout       time.Duration        // 心跳超时时间
	cleanupTicker *time.Ticker         // 清理定时器
	stopChan      chan struct{}        // 停止信号
	metrics       *metrics.Metrics     // 指标统计
	cache         cache.Cache          // Redis缓存
}

// NewHeartbeatService 创建心跳服务实例
// 这是一个轻量级的内存服务，专门用于管理实时在线状态
func NewHeartbeatService(timeout time.Duration, cleanupInterval time.Duration, metrics *metrics.Metrics, cache cache.Cache) *HeartbeatService {
	service := &HeartbeatService{
		heartbeats:    make(map[string]time.Time),
		timeout:       timeout,
		cleanupTicker: time.NewTicker(cleanupInterval),
		stopChan:      make(chan struct{}),
		metrics:       metrics,
		cache:         cache,
	}

	go service.cleanupLoop()
	go service.updateRedisStatsLoop() // 启动Redis统计更新循环
	logger.Info("HeartbeatService started with timeout=%v, cleanup interval=%v", timeout, cleanupInterval)
	return service
}

// UpdateHeartbeat 更新心跳
func (h *HeartbeatService) UpdateHeartbeat(deviceID string) error {
	h.mu.Lock()
	defer h.mu.Unlock()

	h.heartbeats[deviceID] = time.Now()
	h.metrics.RecordRequest()
	return nil
}

// IsAlive 检查设备是否存活
func (h *HeartbeatService) IsAlive(deviceID string) bool {
	h.mu.RLock()
	defer h.mu.RUnlock()

	lastHeartbeat, exists := h.heartbeats[deviceID]
	if !exists {
		h.metrics.RecordError()
		return false
	}

	alive := time.Since(lastHeartbeat) < h.timeout
	if !alive {
		h.metrics.RecordError()
	}
	return alive
}

// cleanupLoop 清理过期心跳的循环
func (h *HeartbeatService) cleanupLoop() {
	for {
		select {
		case <-h.cleanupTicker.C:
			h.cleanup()
		case <-h.stopChan:
			h.cleanupTicker.Stop()
			return
		}
	}
}

// cleanup 清理过期心跳
func (h *HeartbeatService) cleanup() {
	h.mu.Lock()
	defer h.mu.Unlock()

	now := time.Now()
	for deviceID, lastHeartbeat := range h.heartbeats {
		if now.Sub(lastHeartbeat) >= h.timeout {
			delete(h.heartbeats, deviceID)
			h.metrics.RecordError()
		}
	}
}

// Stop 停止心跳服务
func (h *HeartbeatService) Stop() {
	close(h.stopChan)
}

// GetOnlineUserCount 获取当前在线用户数量
func (h *HeartbeatService) GetOnlineUserCount() uint32 {
	h.mu.RLock()
	defer h.mu.RUnlock()

	now := time.Now()
	activeCount := 0
	for _, lastHeartbeat := range h.heartbeats {
		if now.Sub(lastHeartbeat) < h.timeout {
			activeCount++
		}
	}

	return uint32(activeCount)
}

// GetTotalDeviceCount 获取总设备数量
func (h *HeartbeatService) GetTotalDeviceCount() int {
	h.mu.RLock()
	defer h.mu.RUnlock()

	return len(h.heartbeats)
}

// 定期更新Redis统计数据
func (h *HeartbeatService) updateRedisStatsLoop() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒更新一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			h.updateRedisStats()
		case <-h.stopChan:
			return
		}
	}
}

// 更新Redis中的心跳统计数据
func (h *HeartbeatService) updateRedisStats() {
	if h.cache == nil {
		return
	}

	stats := h.GetStats()
	if err := h.cache.Set("bns:heartbeat_stats", stats, 2*time.Minute); err != nil {
		logger.Error("更新Redis心跳统计数据失败: %v", err)
	}
}

// 获取心跳服务统计信息
func (h *HeartbeatService) GetStats() map[string]interface{} {
	h.mu.RLock()
	defer h.mu.RUnlock()

	now := time.Now()
	activeCount := 0
	for _, lastHeartbeat := range h.heartbeats {
		if now.Sub(lastHeartbeat) < h.timeout {
			activeCount++
		}
	}

	stats := map[string]interface{}{
		"total_devices":    len(h.heartbeats),
		"active_devices":   activeCount,
		"inactive_devices": len(h.heartbeats) - activeCount,
		"timestamp":        now.Unix(),
	}

	return stats
}
