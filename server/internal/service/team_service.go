package service

import (
	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// 团队信息服务
type TeamService struct {
	db    *gorm.DB
	cache cache.Cache
}

func NewTeamService(db *gorm.DB, cache cache.Cache) *TeamService {
	return &TeamService{
		db:    db,
		cache: cache,
	}
}

// 获取团队信息
func (s *TeamService) GetTeamInfo() ([]model.Team, error) {
	// 尝试从缓存获取
	var cachedMembers []model.Team
	if err := s.cache.Get("team_info", &cachedMembers); err == nil {
		logger.Debug("从缓存获取团队信息成功: 共 %d 人", len(cachedMembers))
		return cachedMembers, nil
	}

	var members []model.Team
	if err := s.db.Find(&members).Error; err != nil {
		logger.Error("查询团队信息失败: %v", err)
		return nil, err
	}

	// 永久缓存
	if err := s.cache.Set("team_info", members, 0); err != nil {
		logger.Warn("缓存团队信息失败: %v", err)
	}

	logger.Debug("获取团队信息成功: 共 %d 人", len(members))
	return members, nil
}

// 清除团队信息缓存
func (s *TeamService) ClearTeamInfoCache() error {
	cacheKey := "team_info"
	if err := s.cache.Delete(cacheKey); err != nil {
		logger.Warn("清除团队信息缓存失败: %v", err)
		return err
	}
	logger.Debug("团队信息缓存已清除")
	return nil
}
