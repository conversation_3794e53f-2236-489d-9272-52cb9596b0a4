package service

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"udp-server/server/internal/model"
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// 用户认证服务
type AuthService struct {
	db                *gorm.DB
	cache             cache.Cache
	mu                sync.RWMutex
	tokens            map[string]int64 // token -> qqNumber 映射
	userTokens        map[int64]string // qqNumber -> token 映射（用于单点登录）
	maxRetries        int
	permissionService *PermissionService  // 权限服务实例
	riskService       *RiskControlService // 风控服务实例
}

func NewAuthService(db *gorm.DB, cache cache.Cache, riskService *RiskControlService) *AuthService {
	service := &AuthService{
		db:          db,
		cache:       cache,
		tokens:      make(map[string]int64),
		userTokens:  make(map[int64]string),
		maxRetries:  3,
		riskService: riskService,
	}

	// 启动定期更新统计数据的循环
	go service.updateRedisStatsLoop()
	logger.Info("AuthService started with periodic Redis stats update")
	return service
}

// 设置权限服务实例（避免循环依赖）
func (s *AuthService) SetPermissionService(permissionService *PermissionService) {
	s.permissionService = permissionService
}

// 获取缓存实例
func (s *AuthService) GetCache() cache.Cache {
	return s.cache
}

// ==================== 用户认证 ==================== //

// 用户登录
func (s *AuthService) Login(qqNumber int64, deviceInfo string, deviceData *model.LoginRequest, ip string) (*model.User, error) {
	var user model.User
	if err := s.db.Where("uin = ?", qqNumber).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 用户不存在，创建新的普通用户
			if qqNumber <= 0 {
				return nil, binary.NewProtocolError(binary.ErrorCodeInvalidRequest, "无效的QQ号")
			}

			user = model.User{
				Uin:       uint64(qqNumber),
				Name:      fmt.Sprintf("%d", qqNumber),
				Status:    0,
				Email:     "",
				Token:     uuid.New().String(),
				TokenTime: time.Now().Unix(),
				LoginTime: time.Now().Unix(),
			}

			// 创建新用户
			if err := s.db.Create(&user).Error; err != nil {
				return nil, binary.NewProtocolError(binary.ErrorCodeServerError, fmt.Sprintf("创建用户失败: %v", err))
			}
		} else {
			return nil, binary.NewProtocolError(binary.ErrorCodeServerError, fmt.Sprintf("查询用户失败: %v", err))
		}
	} else {
		// 用户存在，封禁状态检查已在 handleLoginRequest 中完成，这里不需要重复检查
		// 注意：这里不再检查 user.Status，因为已经在登录请求处理的早期阶段完成了检查
	}

	// 检查是否有设备历史记录（判断是否为首次设备登录）
	var deviceHistoryCount int64
	if err := s.db.Model(&model.DeviceHistory{}).Where("uin = ?", qqNumber).Count(&deviceHistoryCount).Error; err != nil {
		logger.Warn("查询设备历史记录失败: %v", err)
		// 查询失败时，视为非首次登录，继续执行验证流程
		deviceHistoryCount = 1
	}

	// 如果在历史记录中找不到该QQ号的任何设备记录，则视为首次登录
	isFirstLogin := deviceHistoryCount == 0

	// 直接使用设备指纹
	deviceFingerprint := deviceData.DeviceFingerprint

	logger.Debug("用户登录: QQ=%d, 设备指纹=%s, 是否首次设备登录=%v", qqNumber, deviceFingerprint, isFirstLogin)

	// 先创建或更新设备历史记录（无论是否会被封禁都要记录）
	if err := s.createOrUpdateDeviceHistory(qqNumber, deviceFingerprint, ip); err != nil {
		logger.Warn("创建设备历史记录失败: %v", err)
		// 设备历史记录创建失败不阻止登录，但要记录警告
	}

	// 检查同设备码的其他账号是否有封禁状态
	if err := s.checkSameDeviceBanStatus(qqNumber, deviceData.DeviceFingerprint); err != nil {
		return nil, err
	}

	// 如果不是首次登录，检查该QQ最近的登录设备
	if !isFirstLogin {
		var latestDeviceHistory model.DeviceHistory
		if err := s.db.Where("uin = ?", qqNumber).Order("login_time DESC").First(&latestDeviceHistory).Error; err == nil {
			// 如果存在历史记录，且与当前设备不同，则给出警告日志（但允许登录）
			if latestDeviceHistory.Device != deviceFingerprint {
				logger.Warn("用户使用不同设备登录: UIN=%d, 上次登录设备=%s, 当前设备=%s",
					qqNumber, latestDeviceHistory.Device, deviceFingerprint)
			}
		}
	}

	// 实现单点登录：清理该用户的旧会话
	if err := s.clearUserSessions(qqNumber); err != nil {
		logger.Warn("清理用户旧会话失败: QQ=%d, Error=%v", qqNumber, err)
		// 清理失败不影响登录流程，继续执行
	}

	// 计算最终权限
	finalPermission := s.GetPermission(qqNumber)

	// 更新登录时间和token
	now := time.Now().Unix()
	user.LoginTime = now
	user.TokenTime = now
	user.Token = uuid.New().String()

	// 使用事务确保数据一致性
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// 更新用户信息
		if err := tx.Save(&user).Error; err != nil {
			return fmt.Errorf("更新用户信息失败: %v", err)
		}

		// 设备历史记录已在前面创建，这里不需要重复创建
		return nil
	})

	if err != nil {
		return nil, binary.NewProtocolError(binary.ErrorCodeServerError, err.Error())
	}

	// 将用户信息存储到Redis
	userKey := fmt.Sprintf("user:%s", user.Token)
	if err := s.cache.Set(userKey, user, 24*time.Hour); err != nil {
		logger.Error("存储用户信息到缓存失败: %v，使用备用内存缓存", err)
	} else {
		logger.Debug("成功存储用户信息到Redis缓存: QQ=%d, Token=%s, Key=%s", qqNumber, user.Token, userKey)

		// 立即验证缓存是否写入成功
		var testUser model.User
		if err := s.cache.Get(userKey, &testUser); err != nil {
			logger.Error("缓存写入验证失败: Key=%s, Error=%v", userKey, err)
		} else {
			logger.Debug("缓存写入验证成功: QQ=%d, Token匹配=%v", testUser.Uin, testUser.Token == user.Token)
		}
	}

	// 更新内存映射表（无论Redis是否成功）
	s.mu.Lock()
	s.tokens[user.Token] = qqNumber
	s.userTokens[qqNumber] = user.Token
	s.mu.Unlock()

	logger.Info("用户会话已建立: QQ=%d, Token=%s", qqNumber, user.Token)

	// 创建用户副本，用于返回给客户端
	// 注意：权限信息现在通过CDKey动态计算，不再存储在user表中
	userResponse := user

	logger.Debug("权限信息: QQ=%d, 计算出的权限等级=%d",
		qqNumber, finalPermission)

	return &userResponse, nil
}

// 计算最终权限
func (s *AuthService) GetPermission(qqNumber int64) uint8 {
	// 通过QQ号获取用户UID
	var user model.User
	if err := s.db.Where("uin = ?", qqNumber).First(&user).Error; err != nil {
		logger.Debug("获取用户信息失败: QQ=%d, Error=%v", qqNumber, err)
		return 0 // 返回普通用户
	}

	// 使用新的权限等级计算方法
	if s.permissionService != nil {
		level, err := s.permissionService.GetUserPermissionLevel(user.UID, "client")
		if err != nil {
			logger.Debug("权限服务查询失败: QQ=%d, UID=%d, Error=%v", qqNumber, user.UID, err)
			return 0
		}
		logger.Debug("权限计算: QQ=%d, UID=%d, CDKey权限等级=%d", qqNumber, user.UID, level)
		return level
	} else {
		// 权限服务未设置，返回默认权限
		logger.Debug("权限服务未设置，返回默认权限: QQ=%d, UID=%d", qqNumber, user.UID)
		return 0
	}
}

// 验证用户凭证
func (s *AuthService) ValidateToken(token string) (interface{}, error) {
	// 读取字符串方法会带终止符，这会影响哈希比较
	var user model.User
	userKey := fmt.Sprintf("user:%s", strings.TrimRight(token, "\x00"))

	if err := s.cache.Get(userKey, &user); err != nil {
		if err == cache.ErrCacheMiss {
			// 缓存未命中，检查内存map中是否有此token
			s.mu.RLock()
			qqNumber, exists := s.tokens[token]
			s.mu.RUnlock()

			if exists {
				// 从数据库中获取完整的用户信息
				if err := s.db.Where("uin = ?", qqNumber).First(&user).Error; err != nil {
					logger.Error("登录态过期，请重新登录: Token=%s", token)
					return nil, binary.NewProtocolError(binary.ErrorCodeUnauthorized, "登录态过期，请重新登录")
				}

				// 验证token是否与数据库中的一致
				if user.Token != token {
					logger.Error("登录态过期，请重新登录: Token=%s", token)
					return nil, binary.NewProtocolError(binary.ErrorCodeUnauthorized, "登录态过期，请重新登录")
				}

				// 检查token是否过期
				if time.Now().Unix()-user.TokenTime > 7*86400 {
					logger.Error("登录态过期，请重新登录: Token=%s", token)
					return nil, binary.NewProtocolError(binary.ErrorCodeUnauthorized, "登录态过期，请重新登录")
				}

				// 将用户信息重新写入缓存
				s.cache.Set(userKey, user, 7*24*time.Hour)
				return &user, nil
			}

			logger.Error("用户请求权限不足: Token=%s", token)
			return nil, binary.NewProtocolError(binary.ErrorCodeUnauthorized, "用户请求权限不足")
		}

		logger.Error("服务器内部错误: Token=%s", token)
		return nil, binary.NewProtocolError(binary.ErrorCodeServerError, "服务器内部错误")
	}

	// 检查token是否过期
	if time.Now().Unix()-user.TokenTime > 7*86400 {
		s.cache.Delete(userKey)
		return nil, binary.NewProtocolError(binary.ErrorCodeUnauthorized, "登录态过期，请重新登录")
	}

	return &user, nil
}

// 用户登出
func (s *AuthService) Logout(token string) error {
	// 从缓存中删除用户信息
	userKey := fmt.Sprintf("user:%s", token)
	if err := s.cache.Delete(userKey); err != nil {
		logger.Warn("从缓存中删除用户信息失败: %v", err)
	}

	// 从内存map中移除
	s.mu.Lock()
	qqNumber := s.tokens[token]
	delete(s.tokens, token)
	if qqNumber != 0 {
		delete(s.userTokens, qqNumber)
		logger.Info("用户已登出: QQ=%d, Token=%s", qqNumber, token)

		// 更新设备历史记录状态为离线
		if err := s.updateDeviceStatusToOffline(qqNumber); err != nil {
			logger.Warn("更新设备离线状态失败: QQ=%d, Error=%v", qqNumber, err)
			// 不影响注销流程
		}
	}
	s.mu.Unlock()

	return nil
}

// 获取设备历史
func (a *AuthService) GetDeviceHistory(qqNumber string) ([]string, error) {
	a.mu.RLock()
	defer a.mu.RUnlock()

	// 从缓存中获取设备历史
	key := fmt.Sprintf("devices:%s", qqNumber)
	var devices []string
	if err := a.cache.Get(key, &devices); err != nil {
		return nil, fmt.Errorf("failed to get device history: %v", err)
	}

	return devices, nil
}

// 获取活跃设备
func (a *AuthService) GetActiveDevices(qqNumber string) ([]string, error) {
	a.mu.RLock()
	defer a.mu.RUnlock()

	// 从缓存中获取活跃设备
	key := fmt.Sprintf("active:%s", qqNumber)
	var devices []string
	if err := a.cache.Get(key, &devices); err != nil {
		return nil, fmt.Errorf("failed to get active devices: %v", err)
	}

	return devices, nil
}

// 强制用户下线
func (s *AuthService) KickUser(qqNumber int64) error {
	// 清理用户的所有会话
	if err := s.clearUserSessions(qqNumber); err != nil {
		return fmt.Errorf("清理用户会话失败: %v", err)
	}

	// 清空数据库中的token
	if err := s.db.Model(&model.User{}).Where("uin = ?", qqNumber).Update("token", "").Error; err != nil {
		logger.Warn("清空数据库中的用户token失败: QQ=%d, Error=%v", qqNumber, err)
		// 这个错误不影响主流程，因为会话已经被清理
	}

	// 更新设备历史记录状态为离线
	if err := s.updateDeviceStatusToOffline(qqNumber); err != nil {
		logger.Warn("更新设备离线状态失败: QQ=%d, Error=%v", qqNumber, err)
		// 不影响强制下线流程
	}

	logger.Info("用户已被强制下线: QQ=%d", qqNumber)
	return nil
}

// ==================== 统计监控 ==================== //

// 检查用户封禁状态（用于登录前的快速检查）
func (s *AuthService) CheckUserBanStatus(qqNumber int64) error {
	var user model.User
	if err := s.db.Where("uin = ?", qqNumber).First(&user).Error; err != nil {
		// 用户不存在时不算封禁，允许继续登录流程（会在后续创建新用户）
		if err == gorm.ErrRecordNotFound {
			return nil
		}
		// 数据库查询错误，为了安全起见返回错误
		return binary.NewProtocolError(binary.ErrorCodeServerError, "查询用户状态失败")
	}

	// 检查用户状态
	if user.Status != 0 {
		logger.Debug("用户封禁状态检查: QQ=%d, Status=%d", qqNumber, user.Status)
		return s.createBannedUserError(user.Status)
	}

	return nil
}

// 定期更新统计数据
func (a *AuthService) updateRedisStatsLoop() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒更新一次
	defer ticker.Stop()

	for range ticker.C {
		stats := a.GetStats()
		a.updateRedisStats(stats)
	}
}

// 更新中的统计数据
func (s *AuthService) updateRedisStats(stats map[string]interface{}) {
	if s.cache == nil {
		return
	}

	// 设置统计数据到Redis，过期时间5分钟
	if err := s.cache.Set("bns:online_stats", stats, 5*time.Minute); err != nil {
		logger.Error("更新统计数据失败: %v", err)
	}

	// 同时更新主要的在线数量统计（供PHP快速获取）
	onlineCount := len(s.userTokens) // 基于认证用户的在线数量
	quickStats := map[string]interface{}{
		"online_count": onlineCount,
		"timestamp":    time.Now().Unix(),
		"source":       "auth_service", // 标识数据来源
	}

	if err := s.cache.Set("bns:online_count", quickStats, 5*time.Minute); err != nil {
		logger.Error("更新在线数量失败: %v", err)
	}

	// 更新在线用户列表到Redis
	onlineUsers := s.GetOnlineUsers()
	onlineUserDetails := make([]map[string]interface{}, 0, len(onlineUsers))

	for _, qqNumber := range onlineUsers {
		userInfo := s.GetUserSessionInfo(qqNumber)
		if userInfo["online"].(bool) {
			onlineUserDetails = append(onlineUserDetails, map[string]interface{}{
				"uin":        qqNumber,
				"login_time": userInfo["login_time"],
				"token_time": userInfo["token_time"],
				"online":     true,
			})
		}
	}

	// 设置在线用户详情到Redis
	if err := s.cache.Set("bns:online_users", onlineUserDetails, 5*time.Minute); err != nil {
		logger.Error("更新在线用户列表失败: %v", err)
	}
}

// 更新用户状态
func (s *AuthService) UpdateUserStatus(uin string, isOnline bool) error {
	var user model.User
	if err := s.db.First(&user, "uin = ?", uin).Error; err != nil {
		return fmt.Errorf("failed to find user: %v", err)
	}

	user.Status = 0
	if err := s.db.Save(&user).Error; err != nil {
		return fmt.Errorf("failed to update user status: %v", err)
	}

	return nil
}

// 获取用户最新的设备指纹
func (s *AuthService) GetLatestDeviceFingerprint(uin int64) (string, error) {
	var deviceHistory model.DeviceHistory
	err := s.db.Where("uin = ?", uin).Order("login_time DESC").First(&deviceHistory).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return "", fmt.Errorf("用户没有设备历史记录")
		}
		return "", fmt.Errorf("查询设备历史记录失败: %v", err)
	}

	// 直接返回存储的设备标识
	return deviceHistory.Device, nil
}

// 检查同设备码的其他账号是否有封禁状态
func (s *AuthService) checkSameDeviceBanStatus(currentQQ int64, deviceFingerprint string) error {
	// 查询使用相同设备指纹的所有用户
	var deviceHistories []model.DeviceHistory
	err := s.db.Where("device = ?", deviceFingerprint).
		Select("DISTINCT uin").
		Find(&deviceHistories).Error

	if err != nil {
		logger.Warn("查询同设备用户失败: Device=%s, Error=%v", deviceFingerprint, err)
		return nil // 查询失败不阻止登录
	}

	// 收集所有使用该设备的QQ号
	var qqNumbers []int64
	for _, history := range deviceHistories {
		if history.Uin != currentQQ { // 排除当前登录的QQ号
			qqNumbers = append(qqNumbers, history.Uin)
		}
	}

	if len(qqNumbers) == 0 {
		return nil // 没有其他账号使用该设备
	}

	// 查询这些QQ号对应的用户状态
	var users []model.User
	err = s.db.Where("uin IN ?", qqNumbers).Find(&users).Error
	if err != nil {
		logger.Warn("查询同设备用户状态失败: Device=%s, Error=%v", deviceFingerprint, err)
		return nil // 查询失败不阻止登录
	}

	// 检查是否有封禁状态的账号
	var bannedUsers []model.User
	var maxBanStatus int = 0

	for _, user := range users {
		if user.Status > 0 { // 非正常状态
			bannedUsers = append(bannedUsers, user)
			if user.Status > maxBanStatus {
				maxBanStatus = user.Status
			}
		}
	}

	// 如果发现封禁账号，将当前账号也设置为相同的封禁状态
	if len(bannedUsers) > 0 {
		// 获取当前用户信息
		var currentUser model.User
		err = s.db.Where("uin = ?", currentQQ).First(&currentUser).Error
		if err != nil {
			logger.Warn("查询当前用户失败: QQ=%d, Error=%v", currentQQ, err)
			// 即使查询失败，也要阻止登录
			return s.createBannedUserError(maxBanStatus)
		}

		// 收集所有涉及的QQ号（包括当前QQ和被封禁的QQ）
		allQQs := map[int64]bool{currentQQ: true}
		var bannedQQs []int64
		for _, bannedUser := range bannedUsers {
			bannedQQs = append(bannedQQs, int64(bannedUser.Uin))
			allQQs[int64(bannedUser.Uin)] = true
		}

		// 记录设备传播封禁事件到risk_events表
		if s.riskService != nil {
			description := fmt.Sprintf("设备传播封禁: QQ %d 因同设备存在封禁账号 %v 而被封禁", currentQQ, bannedQQs)
			s.riskService.RecordRiskEvent("device_ban_spread", deviceFingerprint, "", allQQs, len(bannedUsers), "medium", description)
		}

		// 如果当前用户状态正常，则设置为与同设备最严重的封禁状态相同
		if currentUser.Status == 0 {
			currentUser.Status = maxBanStatus
			if err := s.db.Save(&currentUser).Error; err != nil {
				logger.Error("更新用户封禁状态失败: QQ=%d, Status=%d, Error=%v", currentQQ, maxBanStatus, err)
			} else {
				logger.Info("同设备检测：用户 QQ=%d 因同设备存在封禁账号而被设置为封禁状态 %d", currentQQ, maxBanStatus)
				logger.Info("同设备封禁详情: 当前QQ=%d, 设备=%s, 封禁账号=%v, 设置状态=%d",
					currentQQ, deviceFingerprint, bannedQQs, maxBanStatus)
			}
		}

		// 返回封禁错误
		return s.createBannedUserError(maxBanStatus)
	}

	return nil
}

// 根据封禁状态创建相应的错误信息
func (s *AuthService) createBannedUserError(status int) error {
	var statusMessage string
	switch status {
	case 1:
		statusMessage = "当前账号已被暂时封禁，请联系管理员"
	case 2:
		statusMessage = "当前账号已被永久封禁"
	case 3:
		statusMessage = "当前账号因状态异常被冻结，请联系管理员"
	default:
		statusMessage = "未知错误"
	}
	return binary.NewProtocolError(binary.ErrorCodeUnauthorized, statusMessage)
}

// 创建或更新设备历史记录（无事务版本）
func (s *AuthService) createOrUpdateDeviceHistory(uin int64, device string, ip string) error {
	now := time.Now().Unix()

	// 查找是否存在相同设备的记录
	var existingHistory model.DeviceHistory
	err := s.db.Where("uin = ? AND device = ?", uin, device).First(&existingHistory).Error

	if err == nil {
		// 找到相同设备的记录，更新登录时间、IP地址和登录次数
		existingHistory.LoginTime = now
		existingHistory.LogoutTime = nil // 重新登录时清空登出时间
		existingHistory.IPAddress = ip
		existingHistory.Status = "online"
		existingHistory.LoginCount++

		if err := s.db.Save(&existingHistory).Error; err != nil {
			return fmt.Errorf("更新设备历史记录失败: %v", err)
		}

		logger.Debug("更新设备历史记录: UIN=%d, 设备=%s, IP=%s, 登录次数=%d",
			uin, device, ip, existingHistory.LoginCount)

	} else if err == gorm.ErrRecordNotFound {
		// 没有找到相同设备的记录，创建新记录
		deviceHistory := model.NewDeviceHistory(uin, device, ip)
		deviceHistory.LoginTime = now
		deviceHistory.FirstSeen = now
		deviceHistory.LogoutTime = nil // 新登录时清空登出时间

		if err := s.db.Create(deviceHistory).Error; err != nil {
			return fmt.Errorf("创建设备历史记录失败: %v", err)
		}

		logger.Debug("创建新设备历史记录: UIN=%d, 设备=%s, IP=%s", uin, device, ip)

	} else {
		return fmt.Errorf("查询设备历史记录失败: %v", err)
	}

	return nil
}

// 清理用户的所有活跃会话（实现单点登录）
func (s *AuthService) clearUserSessions(qqNumber int64) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 1. 检查内存映射表中是否有该用户的活跃token
	if oldToken, exists := s.userTokens[qqNumber]; exists {
		// 从Redis缓存中删除旧的用户会话
		oldUserKey := fmt.Sprintf("user:%s", oldToken)
		if err := s.cache.Delete(oldUserKey); err != nil {
			logger.Warn("删除Redis中的旧会话失败: Token=%s, Error=%v", oldToken, err)
		} else {
			logger.Info("已删除Redis中的旧会话: QQ=%d, Token=%s", qqNumber, oldToken)
		}

		// 从内存map中删除旧token
		delete(s.tokens, oldToken)
		delete(s.userTokens, qqNumber)

		logger.Info("已删除内存中的旧会话: QQ=%d, Token=%s", qqNumber, oldToken)
	} else {
		logger.Debug("内存映射表中未找到用户的旧token: QQ=%d", qqNumber)
	}

	// 2. 从数据库查找该用户的当前token（作为备用检查）
	var user model.User
	if err := s.db.Where("uin = ?", qqNumber).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 用户不存在，无需清理
			return nil
		}
		return fmt.Errorf("查询用户信息失败: %v", err)
	}

	// 3. 如果数据库中的token与内存中的不一致，也要清理
	if user.Token != "" {
		logger.Debug("数据库中存在token，准备清理: QQ=%d, Token=%s", qqNumber, user.Token)
		oldUserKey := fmt.Sprintf("user:%s", user.Token)
		if err := s.cache.Delete(oldUserKey); err != nil {
			logger.Warn("删除Redis中的数据库token会话失败: Token=%s, Error=%v", user.Token, err)
		}

		// 确保从内存中也删除
		delete(s.tokens, user.Token)

		logger.Info("已清理数据库中的旧token: QQ=%d, Token=%s", qqNumber, user.Token)
	} else {
		logger.Debug("数据库中无token，无需清理: QQ=%d", qqNumber)
	}

	logger.Debug("清理后内存映射表状态: tokens表大小=%d, userTokens表大小=%d", len(s.tokens), len(s.userTokens))
	logger.Debug("完成清理用户会话: QQ=%d", qqNumber)
	return nil
}

// 获取用户的活跃会话数量（用于监控）
func (s *AuthService) GetActiveUserSessions(qqNumber string) (int, error) {
	var user model.User
	if err := s.db.Where("uin = ?", qqNumber).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, nil
		}
		return 0, fmt.Errorf("查询用户信息失败: %v", err)
	}

	// 检查用户是否有有效的token
	if user.Token == "" {
		return 0, nil
	}

	// 检查token是否在缓存中存在
	userKey := fmt.Sprintf("user:%s", user.Token)
	var cachedUser model.User
	if err := s.cache.Get(userKey, &cachedUser); err != nil {
		return 0, nil // 缓存中不存在，说明会话已过期
	}

	// 检查token是否过期
	if time.Now().Unix()-user.TokenTime > 24*3600 {
		return 0, nil // token已过期
	}

	return 1, nil // 有一个活跃会话
}

// 获取认证服务统计信息
func (a *AuthService) GetStats() map[string]interface{} {
	a.mu.RLock()
	defer a.mu.RUnlock()

	stats := map[string]interface{}{
		"total_tokens": len(a.tokens),
		"active_users": len(a.tokens),
		"timestamp":    time.Now().Unix(),
	}

	return stats
}

// ==================== 在线用户管理 ==================== //

// GetOnlineUsers 获取当前在线用户列表（管理员功能）
func (s *AuthService) GetOnlineUsers() []int64 {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var onlineUsers []int64
	for qqNumber := range s.userTokens {
		onlineUsers = append(onlineUsers, qqNumber)
	}

	return onlineUsers
}

// GetTokens 获取当前所有token（用于统计）
func (s *AuthService) GetTokens() map[string]int64 {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 返回tokens的副本以避免并发问题
	tokensCopy := make(map[string]int64, len(s.tokens))
	for token, qqNumber := range s.tokens {
		tokensCopy[token] = qqNumber
	}

	return tokensCopy
}

// IsUserOnline 检查用户是否在线
func (s *AuthService) IsUserOnline(qqNumber int64) bool {
	s.mu.RLock()
	defer s.mu.RUnlock()

	_, exists := s.userTokens[qqNumber]
	return exists
}

// 获取用户会话信息（管理员功能）
func (s *AuthService) GetUserSessionInfo(qqNumber int64) map[string]interface{} {
	s.mu.RLock()
	token, exists := s.userTokens[qqNumber]
	s.mu.RUnlock()

	if !exists {
		return map[string]interface{}{
			"online": false,
			"token":  "",
		}
	}

	// 从缓存获取详细信息
	userKey := fmt.Sprintf("user:%s", token)
	var user model.User
	if err := s.cache.Get(userKey, &user); err != nil {
		return map[string]interface{}{
			"online":    true,
			"token":     token,
			"cache_hit": false,
		}
	}

	return map[string]interface{}{
		"online":     true,
		"token":      token,
		"cache_hit":  true,
		"login_time": user.LoginTime,
		"token_time": user.TokenTime,
	}
}

// 更新设备状态为离线
func (s *AuthService) updateDeviceStatusToOffline(uin int64) error {
	// 更新该用户所有在线设备的状态为离线
	now := time.Now().Unix()
	result := s.db.Model(&model.DeviceHistory{}).
		Where("uin = ? AND status = ?", uin, "online").
		Updates(map[string]interface{}{
			"status":      "offline",
			"logout_time": now, // 记录登出时间
		})

	if result.Error != nil {
		return fmt.Errorf("更新设备离线状态失败: %v", result.Error)
	}

	if result.RowsAffected > 0 {
		logger.Debug("已更新设备离线状态: UIN=%d, 影响记录数=%d, 登出时间=%d", uin, result.RowsAffected, now)
	}

	return nil
}
