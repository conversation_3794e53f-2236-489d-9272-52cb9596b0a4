package service

import (
	"encoding/json"
	"fmt"
	"time"

	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// AnnouncementService 公告服务
type AnnouncementService struct {
	db    *gorm.DB
	cache cache.Cache
}

// NewAnnouncementService 创建公告服务实例
func NewAnnouncementService(db *gorm.DB, cache cache.Cache) *AnnouncementService {
	service := &AnnouncementService{
		db:    db,
		cache: cache,
	}
	return service
}

// 获取公告列表
func (s *AnnouncementService) GetAnnouncements(appType uint8) ([]model.Announcement, error) {
	// 构建缓存键
	cacheKey := fmt.Sprintf("announcement:active:%d", appType)

	// 尝试从缓存获取
	var announcements []model.Announcement
	if err := s.cache.Get(cacheKey, &announcements); err == nil {
		logger.Debug("从缓存获取公告列表，数量: %d", len(announcements))
		return announcements, nil
	}

	// 从数据库查询
	query := s.db.Where("status = ?", 1) // 已发布状态

	// 时间范围过滤
	now := time.Now()
	query = query.Where("(start_time IS NULL OR start_time <= ?)", now)
	query = query.Where("(end_time IS NULL OR end_time >= ?)", now)

	// 应用类型过滤（暂时跳过，因为数据库表中没有target_app_type字段）
	// TODO: 如果需要应用类型过滤，需要在数据库表中添加target_app_type字段
	_ = appType

	err := query.Order("priority DESC, id DESC").Find(&announcements).Error
	if err != nil {
		logger.Error("查询公告失败: %v", err)
		return nil, err
	}

	// 缓存结果
	s.cache.Set(cacheKey, announcements, 7*24*time.Hour)
	logger.Info("从数据库获取公告列表，数量: %d", len(announcements))
	return announcements, nil
}

// 获取特定公告
func (s *AnnouncementService) GetAnnouncement(id uint32) (*model.Announcement, error) {
	// 构建缓存键
	cacheKey := fmt.Sprintf("announcements:detail:%d", id)

	// 尝试从缓存获取
	var announcement model.Announcement
	if err := s.cache.Get(cacheKey, &announcement); err == nil {
		logger.Debug("从缓存获取公告详情: %d", id)
		return &announcement, nil
	}

	// 从数据库查询
	err := s.db.Where("id = ? AND status = ?", id, 1).First(&announcement).Error
	if err != nil {
		return nil, err
	}

	// 缓存结果
	if data, err := json.Marshal(announcement); err == nil {
		s.cache.Set(cacheKey, string(data), 7*24*time.Hour)
	}

	return &announcement, nil
}

// 清除特定公告的详情缓存
func (s *AnnouncementService) UpdateAnnouncementCache(id uint32) {
	s.cache.Delete(fmt.Sprintf("announcement:detail:%d", id))
	s.cache.Delete(fmt.Sprintf("announcement:active:%d", 1)) // APP类型对应的公告列表
}

// 获取当前公告版本号
func (s *AnnouncementService) GetCurrentVersion() uint16 {
	// 尝试从缓存获取
	var cachedVersion uint16
	if err := s.cache.Get("announcement:version", &cachedVersion); err == nil {
		logger.Debug("从缓存获取公告版本号: %d", cachedVersion)
		return cachedVersion
	}

	return 1
}

// 增加公告版本
func (s *AnnouncementService) IncrementVersion() error {
	// 获取版本号
	currentVersion := s.GetCurrentVersion()
	newVersion := currentVersion + 1

	// 更新缓存中的版本号和最后修改时间
	s.cache.Set("announcement:version", newVersion, 7*24*time.Hour)
	logger.Debug("公告版本号已更新: %d -> %d", currentVersion, newVersion)
	return nil
}
