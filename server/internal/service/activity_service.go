package service

import (
	"fmt"
	"time"

	"gorm.io/gorm"

	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"
)

type ActivityService struct {
	db    *gorm.DB
	cache cache.Cache
}

func NewActivityService(db *gorm.DB, cache cache.Cache) *ActivityService {
	return &ActivityService{
		db:    db,
		cache: cache,
	}
}

// 获取有效的活动列表
func (s *ActivityService) GetActivities() ([]model.Activity, error) {
	// 构建缓存键
	cacheKey := "activity:active"
	var activities []model.Activity

	// 尝试从缓存获取
	if err := s.cache.Get(cacheKey, &activities); err == nil {
		logger.Debug("从缓存获取活动列表，数量: %d", len(activities))
		return activities, nil
	}

	// 从数据库查询有效活动
	now := time.Now()
	err := s.db.
		Preload("Flows", "status = ? ORDER BY sort_order ASC", 1).
		Preload("Flows.GroupInfo").
		Where("status = ? AND begin_time <= ? AND end_time >= ?", 1, now, now).
		Order("priority DESC, activity_id DESC").
		Find(&activities).Error

	if err != nil {
		logger.Error("从数据库查询活动列表失败: %v", err)
		return nil, err
	}

	// 缓存查询结果
	if len(activities) > 0 {
		s.cache.Set(cacheKey, activities, 7*24*time.Hour)
		logger.Debug("缓存活动列表，数量: %d", len(activities))
	}

	return activities, nil
}

// 获取特定的活动详情
func (s *ActivityService) GetActivity(id uint64) (*model.Activity, error) {
	logger.Debug("获取活动详情请求: %d", id)

	// 先尝试从缓存获取
	cacheKey := fmt.Sprintf("activity:%d", id)
	var activity model.Activity
	if err := s.cache.Get(cacheKey, &activity); err == nil {
		return &activity, nil
	}

	// 查询数据库
	now := time.Now()
	err := s.db.
		Preload("Flows", "status = ? ORDER BY sort_order ASC", 1).
		Preload("Flows.GroupInfo").
		Where("activity_id = ? AND status = ? AND begin_time <= ? AND end_time >= ?", id, 1, now, now).
		First(&activity).Error

	if err != nil {
		logger.Error("查询活动详情失败: ID=%d, Error=%v", id, err)
		return nil, fmt.Errorf("活动不存在或已失效")
	}

	logger.Debug("从数据库获取活动详情成功: ID=%d, Name=%s, 流程数=%d", activity.ActivityId, activity.ActivityName, len(activity.Flows))

	// 调试：检查分组信息是否正确加载
	for _, flow := range activity.Flows {
		if flow.Group != 0 {
			logger.Debug("流程分组信息: FlowID=%d, FlowName=%s, Group=%d, GroupInfo=%v",
				flow.FlowId, flow.FlowName, flow.Group, flow.GroupInfo)
		}
	}

	// 缓存响应结果
	s.cache.Set(cacheKey, activity, 7*24*time.Hour)
	return &activity, nil
}

// 清理特定活动缓存
func (s *ActivityService) UpdateCache(id uint64) {
	s.cache.Delete(fmt.Sprintf("activity:%d", id))
	s.cache.Delete("activity:active")

	s.IncrementVersion()
}

// 获取活动总版本
func (s *ActivityService) GetCurrentVersion() uint16 {
	// 尝试从缓存获取
	var cachedVersion uint16
	if err := s.cache.Get("activity:version", &cachedVersion); err == nil {
		logger.Debug("从缓存获取活动版本号: %d", cachedVersion)
		return cachedVersion
	}

	return 1
}

// 增加活动总版本
func (s *ActivityService) IncrementVersion() error {
	// 获取版本号
	currentVersion := s.GetCurrentVersion()
	newVersion := currentVersion + 1

	// 更新缓存中的版本号和最后修改时间
	s.cache.Set("activity:version", newVersion, 7*24*time.Hour)
	logger.Debug("活动版本号已更新: %d -> %d", currentVersion, newVersion)
	return nil
}
