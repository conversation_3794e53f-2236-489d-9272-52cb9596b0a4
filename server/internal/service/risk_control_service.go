package service

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"
	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// 风控服务
type RiskControlService struct {
	db               *gorm.DB
	cache            cache.Cache
	config           *model.RiskControlConfigData
	configMutex      sync.RWMutex
	lastConfigUpdate time.Time
}

// 创建风控服务实例
func NewRiskControlService(db *gorm.DB, cache cache.Cache) *RiskControlService {
	service := &RiskControlService{
		db:    db,
		cache: cache,
	}

	// 自动迁移风险事件表和配置表
	if err := db.AutoMigrate(&model.RiskEvent{}, &model.RiskControlConfig{}); err != nil {
		logger.Error("风控服务表迁移失败: %v", err)
	}

	// 初始化配置
	if err := service.loadConfigFromDB(); err != nil {
		logger.Error("加载风控配置失败，使用默认配置: %v", err)
		service.config = model.DefaultRiskControlConfig()
	}

	return service
}

// 检查登录风险
func (s *RiskControlService) CheckLoginRisk(qqNumber int64, deviceID, ipAddress string) error {
	config := s.getConfig()

	// 1. 检查同设备登录不同QQ号
	if err := s.checkDeviceQQLimit(deviceID, qqNumber, config.MaxQQPerDevicePerDay); err != nil {
		return err
	}

	// 2. 检查同IP登录不同QQ号
	if err := s.checkIPQQLimit(ipAddress, qqNumber, config.MaxQQPerIPPerDay); err != nil {
		return err
	}

	// 3. 检查单账号多IP登录（超过阈值时进行地理位置检查）
	if err := s.checkQQMultiDeviceLimit(qqNumber, deviceID, ipAddress, config.MaxDevicePerQQPerDay); err != nil {
		return err
	}

	// 4. 检查频繁登录 (使用 MaxIPPerQQPerDay 作为频繁登录限制)
	if err := s.checkFrequentLogin(qqNumber, deviceID, ipAddress, config.MaxIPPerQQPerDay); err != nil {
		return err
	}

	return nil
}

// 检查同设备登录不同QQ号的限制
func (s *RiskControlService) checkDeviceQQLimit(deviceID string, currentQQ int64, maxLimit int) error {
	// 获取今天的开始时间
	today := time.Now().Truncate(24 * time.Hour)
	todayUnix := today.Unix()

	// 查询今天该设备登录的所有不同QQ号
	var histories []model.DeviceHistory
	err := s.db.Where("device = ? AND login_time >= ?", deviceID, todayUnix).
		Select("DISTINCT uin").
		Find(&histories).Error

	if err != nil {
		log.Printf("[ERROR] 查询设备登录历史失败: %v", err)
		return nil // 查询失败不阻止登录
	}

	// 统计不同的QQ号
	qqSet := make(map[int64]bool)
	for _, history := range histories {
		qqSet[history.Uin] = true
	}

	// 如果当前QQ不在列表中，添加进去
	qqSet[currentQQ] = true

	uniqueQQCount := len(qqSet)

	log.Printf("[DEBUG] 设备 %s 今日登录QQ数量: %d/%d", deviceID, uniqueQQCount, maxLimit)

	// 如果超过限制，触发风控
	if uniqueQQCount > maxLimit {
		// 记录风险事件
		s.RecordRiskEvent("device_qq_limit", deviceID, "", qqSet, uniqueQQCount, "high",
			fmt.Sprintf("设备 %s 今日登录QQ数量超限: %d/%d", deviceID, uniqueQQCount, maxLimit))

		// 将涉及的所有QQ号设置为审核状态
		s.setUsersToAuditStatus(qqSet, fmt.Sprintf("设备异常登录检测：设备 %s 今日登录QQ数量超限", deviceID))

		return fmt.Errorf("设备异常登录检测：该设备今日登录账号数量过多，相关账号已进入审核状态")
	}

	return nil
}

// 检查单账号多IP登录限制（超过阈值时进行地理位置检查）
func (s *RiskControlService) checkQQMultiDeviceLimit(qqNumber int64, deviceID, ipAddress string, maxLimit int) error {
	// 获取今天的开始时间
	today := time.Now().Truncate(24 * time.Hour)
	todayUnix := today.Unix()

	// 查询今天该QQ号登录的所有设备记录
	var histories []model.DeviceHistory
	err := s.db.Where("uin = ? AND login_time >= ?", qqNumber, todayUnix).
		Select("device, ip_address").
		Find(&histories).Error

	if err != nil {
		log.Printf("[ERROR] 查询QQ多设备登录历史失败: %v", err)
		return nil // 查询失败不阻止登录
	}

	// 先统计不同的IP地址数量
	uniqueIPs := make(map[string]bool)
	for _, history := range histories {
		uniqueIPs[history.IPAddress] = true
	}

	// 添加当前IP
	uniqueIPs[ipAddress] = true
	uniqueIPCount := len(uniqueIPs)
	log.Printf("[DEBUG] QQ %d 今日登录IP数量: %d/%d", qqNumber, uniqueIPCount, maxLimit)

	// 如果IP数量没有超过限制，直接通过
	if uniqueIPCount <= maxLimit {
		return nil
	}

	// IP数量超过限制，进行地理位置检查
	log.Printf("[DEBUG] QQ %d IP数量超限，开始地理位置检查", qqNumber)

	// 获取今日该用户所有IP的城市信息（使用缓存）
	userLocationCacheKey := fmt.Sprintf("user_daily_locations:%d:%s", qqNumber, today.Format("2006-01-02"))
	var userLocations map[string]string // key: IP, value: 省份-城市

	if s.cache != nil {
		if err := s.cache.Get(userLocationCacheKey, &userLocations); err != nil {
			userLocations = make(map[string]string)
		}
	} else {
		userLocations = make(map[string]string)
	}

	// 检查当前IP的地理位置
	if _, exists := userLocations[ipAddress]; !exists {
		currentLocation, err := s.GetLocation(ipAddress)
		if err != nil {
			logger.Warn("获取当前IP地理位置失败，使用IP比较: IP=%s, Error=%v", ipAddress, err)
			// 如果获取失败，回退到原来的IP比较逻辑
			return s.checkQQMultiDeviceLimitByIP(qqNumber, deviceID, ipAddress, maxLimit, histories)
		}
		userLocations[ipAddress] = fmt.Sprintf("%s-%s", currentLocation.Province, currentLocation.City)
	}

	// 检查历史IP的地理位置
	for _, history := range histories {
		if _, exists := userLocations[history.IPAddress]; !exists {
			historyLocation, err := s.GetLocation(history.IPAddress)
			if err != nil {
				logger.Warn("获取历史IP地理位置失败，按不同城市处理: IP=%s, Error=%v", history.IPAddress, err)
				// 获取失败时使用IP作为唯一标识
				userLocations[history.IPAddress] = history.IPAddress
			} else {
				userLocations[history.IPAddress] = fmt.Sprintf("%s-%s", historyLocation.Province, historyLocation.City)
			}
		}
	}

	// 更新缓存（缓存到当天结束）
	if s.cache != nil {
		tomorrow := today.Add(24 * time.Hour)
		cacheExpiry := tomorrow.Sub(time.Now())
		if err := s.cache.Set(userLocationCacheKey, userLocations, cacheExpiry); err != nil {
			logger.Warn("缓存用户地理位置信息失败: %v", err)
		}
	}

	// 统计不同的城市数量
	uniqueLocations := make(map[string]bool)
	for _, location := range userLocations {
		uniqueLocations[location] = true
	}

	uniqueLocationCount := len(uniqueLocations)
	currentLocationKey := userLocations[ipAddress]

	log.Printf("[DEBUG] QQ %d 今日登录城市数量: %d/%d (当前位置: %s)", qqNumber, uniqueLocationCount, maxLimit, currentLocationKey)

	// 分析城市分布情况
	if uniqueLocationCount <= 2 {
		// 1-2个城市的登录 - 记录低风险事件，但不设置用户为审核状态
		qqSet := map[int64]bool{qqNumber: true}

		// 构建城市列表用于描述
		var cities []string
		for city := range uniqueLocations {
			cities = append(cities, city)
		}

		eventType := "qq_multi_device_same_city"
		description := fmt.Sprintf("QQ %d 今日同城多IP登录: %d个IP，城市: %s", qqNumber, uniqueIPCount, cities[0])

		if uniqueLocationCount == 2 {
			eventType = "qq_multi_device_two_cities"
			description = fmt.Sprintf("QQ %d 今日两城市登录: %d个IP，城市: %v", qqNumber, uniqueIPCount, cities)
		}

		s.RecordRiskEvent(eventType, deviceID, ipAddress, qqSet, uniqueLocationCount, "low", description)
		return nil // 不影响用户正常使用
	} else {
		// 3个或更多城市的登录 - 记录中等风险事件，设置用户为审核状态
		qqSet := map[int64]bool{qqNumber: true}

		// 构建城市列表用于描述
		var cities []string
		for city := range uniqueLocations {
			cities = append(cities, city)
		}

		s.RecordRiskEvent("qq_multi_device_multi_city", deviceID, ipAddress, qqSet, uniqueLocationCount, "medium",
			fmt.Sprintf("QQ %d 今日多城市登录: %d个城市 (%v)", qqNumber, uniqueLocationCount, cities))

		// 将该QQ号设置为审核状态
		s.setUsersToAuditStatus(qqSet, fmt.Sprintf("多设备登录检测：QQ %d 今日多城市登录", qqNumber))
		return fmt.Errorf("该账号今日登录城市数量过多，已进入审核状态")
	}
}

// 使用IP比较的备用方法
func (s *RiskControlService) checkQQMultiDeviceLimitByIP(qqNumber int64, deviceID, ipAddress string, maxLimit int, histories []model.DeviceHistory) error {
	// 统计不同的设备（IP相同时算作单设备）
	uniqueDevices := make(map[string]bool) // key: IP地址，value: 是否存在
	for _, history := range histories {
		uniqueDevices[history.IPAddress] = true
	}

	// 添加当前设备（如果IP不同）
	uniqueDevices[ipAddress] = true
	uniqueDeviceCount := len(uniqueDevices)
	log.Printf("[DEBUG] QQ %d 今日登录设备数量: %d/%d (IP相同算单设备，备用方法)", qqNumber, uniqueDeviceCount, maxLimit)

	// 如果超过限制，触发风控
	if uniqueDeviceCount > maxLimit {
		// 记录风险事件（无法获取地理位置时设置为中等风险，需要审核）
		qqSet := map[int64]bool{qqNumber: true}
		s.RecordRiskEvent("qq_multi_device_unknown_location", deviceID, ipAddress, qqSet, uniqueDeviceCount, "medium",
			fmt.Sprintf("QQ %d 今日多IP登录且无法获取地理位置: %d个IP (需人工审核)", qqNumber, uniqueDeviceCount))

		// 将该QQ号设置为审核状态
		s.setUsersToAuditStatus(qqSet, fmt.Sprintf("多设备登录检测：QQ %d 今日多IP登录且无法获取地理位置", qqNumber))

		return fmt.Errorf("该账号今日登录设备数量过多，已进入审核状态")
	}

	return nil
}

// 检查同IP登录不同QQ号的限制
func (s *RiskControlService) checkIPQQLimit(ipAddress string, currentQQ int64, maxLimit int) error {
	// 获取今天的开始时间
	today := time.Now().Truncate(24 * time.Hour)
	todayUnix := today.Unix()

	// 查询今天该IP登录的所有不同QQ号
	var histories []model.DeviceHistory
	err := s.db.Where("ip_address = ? AND login_time >= ?", ipAddress, todayUnix).
		Select("DISTINCT uin").
		Find(&histories).Error

	if err != nil {
		log.Printf("[ERROR] 查询IP登录历史失败: %v", err)
		return nil // 查询失败不阻止登录
	}

	// 统计不同的QQ号
	qqSet := make(map[int64]bool)
	for _, history := range histories {
		qqSet[history.Uin] = true
	}

	// 如果当前QQ不在列表中，添加进去
	qqSet[currentQQ] = true
	uniqueQQCount := len(qqSet)
	log.Printf("[DEBUG] IP %s 今日登录QQ数量: %d/%d", ipAddress, uniqueQQCount, maxLimit)

	// 如果超过限制，触发风控
	if uniqueQQCount > maxLimit {
		// 记录风险事件
		s.RecordRiskEvent("ip_qq_limit", "", ipAddress, qqSet, uniqueQQCount, "medium",
			fmt.Sprintf("IP %s 今日登录QQ数量超限: %d/%d", ipAddress, uniqueQQCount, maxLimit))

		// 将涉及的所有QQ号设置为审核状态
		s.setUsersToAuditStatus(qqSet, fmt.Sprintf("IP异常登录检测：IP %s 今日登录QQ数量超限", ipAddress))
		return fmt.Errorf("IP异常登录检测：该IP今日登录账号数量过多，相关账号已进入审核状态")
	}

	return nil
}

// 检查频繁登录
func (s *RiskControlService) checkFrequentLogin(qqNumber int64, deviceID, ipAddress string, maxLimit int) error {
	// 获取一小时前的时间
	oneHourAgo := time.Now().Add(-time.Hour).Unix()

	// 查询该QQ号在过去一小时内的登录次数
	var count int64
	err := s.db.Model(&model.DeviceHistory{}).
		Where("uin = ? AND login_time >= ?", qqNumber, oneHourAgo).
		Count(&count).Error

	if err != nil {
		log.Printf("[ERROR] 查询频繁登录失败: %v", err)
		return nil // 查询失败不阻止登录
	}

	log.Printf("[DEBUG] QQ %d 过去1小时登录次数: %d/%d", qqNumber, count, maxLimit)

	// 如果超过限制，触发风控
	if int(count) > maxLimit {
		// 记录风险事件
		qqSet := map[int64]bool{qqNumber: true}
		s.RecordRiskEvent("frequent_login", deviceID, ipAddress, qqSet, int(count), "medium",
			fmt.Sprintf("QQ %d 频繁登录: %d次/小时", qqNumber, count))

		// 将该QQ号设置为审核状态
		s.setUsersToAuditStatus(qqSet, fmt.Sprintf("频繁登录检测：QQ %d 过去1小时登录次数过多", qqNumber))
		return fmt.Errorf("频繁登录检测：该账号登录过于频繁，已进入审核状态")
	}

	return nil
}

// 记录风险事件（公开方法）
func (s *RiskControlService) RecordRiskEvent(eventType, deviceID, ipAddress string, qqSet map[int64]bool, count int, severity, description string) {
	// 将QQ号集合转换为逗号分隔的字符串
	var qqNumbers []string
	for qq := range qqSet {
		qqNumbers = append(qqNumbers, fmt.Sprintf("%d", qq))
	}
	qqNumbersStr := strings.Join(qqNumbers, " ")

	riskEvent := &model.RiskEvent{
		EventType:   eventType,
		DeviceID:    deviceID,
		IPAddress:   ipAddress,
		QQNumbers:   qqNumbersStr,
		Count:       count,
		Severity:    severity,
		Status:      "pending",
		Description: description,
	}

	// 记录到数据库
	if err := s.db.Create(riskEvent).Error; err != nil {
		log.Printf("[ERROR] 记录风险事件失败: %v", err)
	} else {
		log.Printf("[RISK] 风险事件已记录: %s - %s", eventType, description)
	}
}

// 将用户设置为审核状态
func (s *RiskControlService) setUsersToAuditStatus(qqSet map[int64]bool, reason string) {
	for qq := range qqSet {
		var user model.User
		if err := s.db.Where("uin = ?", qq).First(&user).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				log.Printf("[WARN] 用户不存在，跳过状态更新: QQ=%d", qq)
				continue
			}
			log.Printf("[ERROR] 查询用户失败: QQ=%d, Error=%v", qq, err)
			continue
		}

		// 如果用户状态已经是审核状态或更严重的状态，跳过
		if user.Status >= 3 {
			log.Printf("[INFO] 用户状态已是审核或更严重状态，跳过: QQ=%d, Status=%d", qq, user.Status)
			continue
		}

		// 更新用户状态为审核状态
		user.Status = 3
		if err := s.db.Save(&user).Error; err != nil {
			log.Printf("[ERROR] 更新用户状态失败: QQ=%d, Error=%v", qq, err)
		} else {
			log.Printf("[RISK] 用户已设置为审核状态: QQ=%d, Reason=%s", qq, reason)

			// 记录用户日志
			userLog := &model.UserLog{
				UID:   user.UID,
				Type:  "risk_control",
				Extra: reason,
				Time:  time.Now().Format("2006-01-02 15:04:05"),
			}
			if err := s.db.Create(userLog).Error; err != nil {
				log.Printf("[ERROR] 记录用户日志失败: QQ=%d, Error=%v", qq, err)
			}
		}
	}
}

// 从数据库加载配置
func (s *RiskControlService) loadConfigFromDB() error {
	s.configMutex.Lock()
	defer s.configMutex.Unlock()

	var configs []model.RiskControlConfig
	if err := s.db.Find(&configs).Error; err != nil {
		return fmt.Errorf("查询配置失败: %v", err)
	}

	// 创建配置映射
	configMap := make(map[string]string)
	for _, config := range configs {
		configMap[config.ConfigKey] = config.ConfigValue
	}

	// 解析配置
	riskConfig := &model.RiskControlConfigData{}

	if val, ok := configMap["max_qq_per_device_per_day"]; ok {
		if intVal, err := strconv.Atoi(val); err == nil {
			riskConfig.MaxQQPerDevicePerDay = intVal
		}
	}

	if val, ok := configMap["max_qq_per_ip_per_day"]; ok {
		if intVal, err := strconv.Atoi(val); err == nil {
			riskConfig.MaxQQPerIPPerDay = intVal
		}
	}

	if val, ok := configMap["max_device_per_qq_per_day"]; ok {
		if intVal, err := strconv.Atoi(val); err == nil {
			riskConfig.MaxDevicePerQQPerDay = intVal
		}
	}

	if val, ok := configMap["max_ip_per_qq_per_day"]; ok {
		if intVal, err := strconv.Atoi(val); err == nil {
			riskConfig.MaxIPPerQQPerDay = intVal
		}
	}

	if val, ok := configMap["enable_device_check"]; ok {
		riskConfig.EnableDeviceCheck = val == "true" || val == "1"
	}

	if val, ok := configMap["enable_ip_check"]; ok {
		riskConfig.EnableIPCheck = val == "true" || val == "1"
	}

	if val, ok := configMap["amap_api_key"]; ok {
		riskConfig.AmapAPIKey = val
	}

	// 设置默认值
	if riskConfig.MaxQQPerDevicePerDay == 0 {
		riskConfig.MaxQQPerDevicePerDay = 3
	}
	if riskConfig.MaxQQPerIPPerDay == 0 {
		riskConfig.MaxQQPerIPPerDay = 3
	}
	if riskConfig.MaxDevicePerQQPerDay == 0 {
		riskConfig.MaxDevicePerQQPerDay = 3
	}
	if riskConfig.MaxIPPerQQPerDay == 0 {
		riskConfig.MaxIPPerQQPerDay = 5
	}
	// 如果数据库中没有配置AmapAPIKey，使用默认值
	if riskConfig.AmapAPIKey == "" {
		riskConfig.AmapAPIKey = "2e2cf46e41f0b0a1b2bef0c0aabd6dfd"
	}

	s.config = riskConfig
	s.lastConfigUpdate = time.Now()

	log.Printf("[INFO] 风控配置已从数据库加载: 设备限制=%d, IP限制=%d, QQ多设备限制=%d, QQ多IP限制=%d",
		riskConfig.MaxQQPerDevicePerDay, riskConfig.MaxQQPerIPPerDay, riskConfig.MaxDevicePerQQPerDay, riskConfig.MaxIPPerQQPerDay)

	return nil
}

// 获取当前配置（线程安全）
func (s *RiskControlService) getConfig() *model.RiskControlConfigData {
	s.configMutex.RLock()
	defer s.configMutex.RUnlock()

	if s.config == nil {
		return model.DefaultRiskControlConfig()
	}

	return s.config
}

// 更新配置（每小时调用）
func (s *RiskControlService) UpdateConfig() error {
	// 检查是否需要更新（避免频繁查询数据库）
	s.configMutex.RLock()
	lastUpdate := s.lastConfigUpdate
	s.configMutex.RUnlock()

	if time.Since(lastUpdate) < 30*time.Minute {
		return nil // 30分钟内不重复更新
	}

	log.Printf("[INFO] 开始更新风控配置...")
	return s.loadConfigFromDB()
}

// 获取地理位置信息
func (s *RiskControlService) GetLocation(ipAddress string) (*model.AmapIPResponse, error) {
	config := s.getConfig()

	if config.AmapAPIKey == "" {
		return nil, fmt.Errorf("高德地图API密钥未配置")
	}

	// 先从缓存获取
	cacheKey := fmt.Sprintf("ip_location:%s", ipAddress)
	var cachedLocation model.AmapIPResponse
	if s.cache != nil {
		if err := s.cache.Get(cacheKey, &cachedLocation); err == nil {
			return &cachedLocation, nil
		}
	}

	// 调用高德地图API
	url := fmt.Sprintf("https://restapi.amap.com/v3/ip?output=json&key=%s&ip=%s", config.AmapAPIKey, ipAddress)
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("调用高德地图API失败: %v", err)
	}
	defer resp.Body.Close()

	var location model.AmapIPResponse
	if err := json.NewDecoder(resp.Body).Decode(&location); err != nil {
		return nil, fmt.Errorf("解析API响应失败: %v", err)
	}

	if location.Status != "1" {
		return nil, fmt.Errorf("API返回错误: %s", location.Info)
	}

	// 缓存结果（30天）
	if s.cache != nil {
		if err := s.cache.Set(cacheKey, location, 30*24*time.Hour); err != nil {
			logger.Warn("缓存IP地理位置失败: %v", err)
		}
	}

	return &location, nil
}
