package service

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
	"gorm.io/gorm"

	"udp-server/server/internal/database"
	"udp-server/server/internal/model"

	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"
)

// StatsService 统计服务
type StatsService struct {
	startTime    time.Time
	db           *gorm.DB
	cache        cache.Cache
	authService  *AuthService
	heartbeatSvc *HeartbeatService
	riskService  *RiskControlService
}

// NewStatsService 创建统计服务
func NewStatsService(db *gorm.DB, cache cache.Cache, authService *AuthService, heartbeatSvc *HeartbeatService, riskService *RiskControlService) *StatsService {
	return &StatsService{
		startTime:    time.Now(),
		db:           db,
		cache:        cache,
		authService:  authService,
		heartbeatSvc: heartbeatSvc,
		riskService:  riskService,
	}
}

// 启动统计数据收集器
func (s *StatsService) StartStatsCollector() {
	// 检查是否为测试服务器模式
	isTestServer := viper.GetBool("test_server.enabled")

	// 每小时保存一次统计数据
	ticker := time.NewTicker(1 * time.Hour)

	go func() {
		defer ticker.Stop()

		for range ticker.C {
			if err := s.SaveCurrentStats(); err != nil {
				logger.Error("保存统计数据失败: %v", err)
			}
		}
	}()

	if isTestServer {
		logger.Info("统计数据收集器已启动（测试服务器模式：不写入 online_stats_history 表）")
	} else {
		logger.Info("统计数据收集器已启动，每小时保存一次数据到 online_stats_history 表")
	}
}

// 启动统计数据清理任务
func (s *StatsService) StartStatsCleanup() {
	// 检查是否为测试服务器模式
	isTestServer := viper.GetBool("test_server.enabled")

	// 每天凌晨3点清理过期数据
	now := time.Now()
	nextCleanup := time.Date(now.Year(), now.Month(), now.Day()+1, 3, 0, 0, 0, now.Location())
	if now.Hour() < 3 {
		nextCleanup = time.Date(now.Year(), now.Month(), now.Day(), 3, 0, 0, 0, now.Location())
	}

	ticker := time.NewTicker(24 * time.Hour)

	go func() {
		defer ticker.Stop()

		// 等待到第一次执行时间
		time.Sleep(time.Until(nextCleanup))

		if isTestServer {
			logger.Info("启动统计数据清理任务（测试服务器模式：不清理 online_stats_history 表）")
		} else {
			logger.Info("启动统计数据清理任务，每天凌晨3点执行")
		}

		for range ticker.C {
			if isTestServer {
				logger.Info("执行统计数据清理任务（测试服务器模式：跳过清理）")
			} else {
				logger.Info("开始执行统计数据清理任务")
				if err := s.CleanupOldStats(); err != nil {
					logger.Error("统计数据清理任务失败: %v", err)
				} else {
					logger.Info("统计数据清理任务完成")
				}
			}
		}
	}()
}

// 保存当前统计数据到历史记录
func (s *StatsService) SaveCurrentStats() error {
	now := time.Now()

	// 更新风控配置（每小时执行一次）
	if s.riskService != nil {
		if err := s.riskService.UpdateConfig(); err != nil {
			logger.Error("更新风控配置失败: %v", err)
		}
	}

	// 检查是否为测试服务器，不需要将统计信息保存到数据库
	isTestServer := viper.GetBool("test_server.enabled")
	if isTestServer {
		return nil
	}

	// 直接获取统计信息，避免不必要的类型转换
	onlineUsers := s.authService.GetOnlineUsers()
	authActiveUsers := len(s.authService.GetTokens())
	authTotalTokens := len(s.authService.GetTokens())
	heartbeatActive := s.heartbeatSvc.GetOnlineUserCount()
	heartbeatTotal := s.heartbeatSvc.GetTotalDeviceCount()
	updateRequestCount := s.getUpdateRequestCount()

	// 创建历史记录
	statsHistory := &model.OnlineStatsHistory{
		OnlineCount:        len(onlineUsers),
		AuthActiveUsers:    authActiveUsers,
		AuthTotalTokens:    authTotalTokens,
		HeartbeatActive:    heartbeatActive,
		HeartbeatTotal:     heartbeatTotal,
		UpdateRequestCount: updateRequestCount,
		Timestamp:          now.Unix(),
	}

	// 保存到数据库
	if err := s.db.Create(statsHistory).Error; err != nil {
		return fmt.Errorf("保存统计历史记录失败: %v", err)
	}

	logger.Info("已保存统计历史记录: 在线用户=%d, 认证用户=%d, 心跳设备=%d, 更新请求=%d",
		statsHistory.OnlineCount, statsHistory.AuthActiveUsers, statsHistory.HeartbeatActive, statsHistory.UpdateRequestCount)
	return nil
}

// 获取历史统计数据
func (s *StatsService) GetHistoryStats(period string, limit int) ([]model.OnlineStatsHistory, error) {
	var stats []model.OnlineStatsHistory
	var interval time.Duration

	// 根据周期确定查询间隔
	switch period {
	case "hour":
		interval = time.Hour
	case "day":
		interval = 24 * time.Hour
	default:
		interval = time.Hour
	}

	// 计算开始时间
	startTime := time.Now().Add(-time.Duration(limit) * interval).Unix()

	// 查询历史数据
	err := s.db.Where("timestamp >= ?", startTime).
		Order("timestamp ASC").
		Limit(limit).
		Find(&stats).Error

	if err != nil {
		return nil, fmt.Errorf("查询历史统计数据失败: %v", err)
	}

	return stats, nil
}

// 获取按时间分组的历史统计数据
func (s *StatsService) GetHistoryStatsGrouped(period string, limit int) ([]model.OnlineStatsHistory, error) {
	var stats []model.OnlineStatsHistory
	var groupBy string
	var interval time.Duration

	// 根据周期确定分组方式和间隔
	switch period {
	case "hour":
		// 按小时分组，取每小时的平均值
		groupBy = "FROM_UNIXTIME(timestamp, '%Y-%m-%d %H:00:00')"
		interval = time.Hour
	case "day":
		// 按天分组，取每天的平均值
		groupBy = "FROM_UNIXTIME(timestamp, '%Y-%m-%d')"
		interval = 24 * time.Hour
	default:
		groupBy = "FROM_UNIXTIME(timestamp, '%Y-%m-%d %H:00:00')"
		interval = time.Hour
	}

	// 计算开始时间
	startTime := time.Now().Add(-time.Duration(limit) * interval).Unix()

	// 查询分组统计数据
	err := s.db.Select(fmt.Sprintf(`
		ROUND(AVG(online_count)) as online_count,
		ROUND(AVG(auth_active_users)) as auth_active_users,
		ROUND(AVG(auth_total_tokens)) as auth_total_tokens,
		ROUND(AVG(heartbeat_active)) as heartbeat_active,
		ROUND(AVG(heartbeat_total)) as heartbeat_total,
		ROUND(AVG(update_request_count)) as update_request_count,
		UNIX_TIMESTAMP(%s) as timestamp
	`, groupBy)).
		Where("timestamp >= ?", startTime).
		Group(groupBy).
		Order("timestamp ASC").
		Limit(limit).
		Find(&stats).Error

	if err != nil {
		return nil, fmt.Errorf("查询分组历史统计数据失败: %v", err)
	}

	return stats, nil
}

// 获取审核状态用户数量
func (s *StatsService) GetReviewUsersCount() (int64, error) {
	var count int64
	err := s.db.Table("user").Where("status = ?", 3).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("查询审核用户数量失败: %v", err)
	}
	return count, nil
}

// 清理过期的统计数据
func (s *StatsService) CleanupOldStats() error {
	// 检查是否为测试服务器
	isTestServer := viper.GetBool("test_server.enabled")
	if isTestServer {
		return nil
	}

	// 保留最近30天的数据
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30).Unix()

	result := s.db.Where("timestamp < ?", thirtyDaysAgo).Delete(&model.OnlineStatsHistory{})
	if result.Error != nil {
		return fmt.Errorf("清理过期统计数据失败: %v", result.Error)
	}

	if result.RowsAffected > 0 {
		logger.Info("已清理过期统计数据: 删除记录数=%d", result.RowsAffected)
	}

	return nil
}

// 获取管理后台统计信息
func (s *StatsService) GetStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 获取总用户数
	var totalUsers int64
	database.GetDB().Model(&model.User{}).Count(&totalUsers)
	stats["total_users"] = totalUsers

	// 获取登录认证统计
	onlineUsers := s.authService.GetOnlineUsers()
	stats["auth_users"] = len(onlineUsers)

	// 获取心跳统计
	stats["online_users"] = s.heartbeatSvc.GetOnlineUserCount()

	// 获取今日启动请求量
	stats["today_request"] = s.getUpdateRequestCount()

	// 获取服务器运行时间
	stats["server_uptime"] = time.Since(s.startTime).String()
	return stats, nil
}

// 获取服务器状态
func (s *StatsService) GetServerStatus() (map[string]interface{}, error) {
	status := make(map[string]interface{})

	// 服务器状态
	status["status"] = "running"

	// 运行时间
	uptime := time.Since(s.startTime)
	hours := int(uptime.Hours())
	minutes := int(uptime.Minutes()) % 60
	status["uptime"] = fmt.Sprintf("%dh %dm", hours, minutes)

	// 版本信息
	status["version"] = "1.0.0"

	// 启动时间
	status["last_restart"] = s.startTime.Format("2006-01-02 15:04:05")

	// 数据库连接状态
	sqlDB, err := s.db.DB()
	if err != nil {
		status["db_status"] = "error"
	} else {
		if err := sqlDB.Ping(); err != nil {
			status["db_status"] = "disconnected"
		} else {
			status["db_status"] = "connected"
		}
	}

	return status, nil
}

// 获取在线用户
func (s *StatsService) GetOnlineUsers() ([]map[string]interface{}, error) {
	onlineUserQQs := s.authService.GetOnlineUsers()
	users := make([]map[string]interface{}, 0, len(onlineUserQQs))

	for _, qqNumber := range onlineUserQQs {
		// 从数据库获取用户详细信息
		var user model.User
		if err := s.db.Where("uin = ?", qqNumber).First(&user).Error; err != nil {
			// 如果数据库中找不到用户，使用基本信息
			userInfo := map[string]interface{}{
				"uin":        qqNumber,
				"name":       fmt.Sprintf("%d", qqNumber),
				"login_time": "未知",
				"status":     0,
			}
			users = append(users, userInfo)
			continue
		}

		userInfo := map[string]interface{}{
			"uin":        user.Uin,
			"name":       user.Name,
			"login_time": time.Unix(user.LoginTime, 0).Format("2006-01-02 15:04:05"),
			"status":     user.Status,
		}
		users = append(users, userInfo)
	}

	return users, nil
}

// 获取错误统计
func (s *StatsService) GetErrorStats(period string) (map[string]interface{}, error) {
	logger.Info("获取错误统计: period=%s", period)
	stats := make(map[string]interface{})

	// 根据时间段计算起始时间
	var startTime time.Time
	now := time.Now()

	switch period {
	case "hour":
		startTime = now.Add(-time.Hour)
	case "day":
		startTime = now.Add(-24 * time.Hour)
	case "week":
		startTime = now.Add(-7 * 24 * time.Hour)
	case "month":
		startTime = now.Add(-30 * 24 * time.Hour)
	default:
		startTime = now.Add(-24 * time.Hour) // 默认一天
	}

	// 从风控事件表统计错误（作为错误事件的代理）
	var totalErrors int64
	if err := s.db.Model(&model.RiskEvent{}).
		Where("created_at >= ? AND severity = ?", startTime, "high").
		Count(&totalErrors).Error; err != nil {
		logger.Error("获取错误统计失败: %v", err)
		totalErrors = 0
	}

	stats["total_errors"] = totalErrors
	stats["error_rate"] = 2.5 // 模拟错误率
	stats["period"] = period

	// 获取错误类型统计
	var topErrors []map[string]interface{}
	var eventTypes []struct {
		EventType string
		Count     int64
	}

	if err := s.db.Model(&model.RiskEvent{}).
		Select("event_type, COUNT(*) as count").
		Where("created_at >= ? AND severity = ?", startTime, "high").
		Group("event_type").
		Order("count DESC").
		Limit(5).
		Scan(&eventTypes).Error; err == nil {

		for _, et := range eventTypes {
			topErrors = append(topErrors, map[string]interface{}{
				"type":  et.EventType,
				"count": et.Count,
			})
		}
	}

	stats["top_errors"] = topErrors
	return stats, nil
}

// 获取API调用统计
func (s *StatsService) GetAPIStats(period string) (map[string]interface{}, error) {
	logger.Info("获取API调用统计: period=%s", period)
	stats := make(map[string]interface{})

	// 根据时间段计算起始时间
	var startTime time.Time
	now := time.Now()

	switch period {
	case "hour":
		startTime = now.Add(-time.Hour)
	case "day":
		startTime = now.Add(-24 * time.Hour)
	case "week":
		startTime = now.Add(-7 * 24 * time.Hour)
	case "month":
		startTime = now.Add(-30 * 24 * time.Hour)
	default:
		startTime = now.Add(-24 * time.Hour) // 默认一天
	}

	// 从更新请求统计中获取API调用数据
	var totalCalls int64 = 0

	// 获取更新请求统计作为API调用的代理
	if s.cache != nil {
		cacheKey := fmt.Sprintf("update_request_count:%s", startTime.Format("2006-01-02"))
		var updateRequests int64
		if err := s.cache.Get(cacheKey, &updateRequests); err == nil {
			totalCalls += updateRequests
		}
	}

	stats["total_calls"] = totalCalls
	stats["success_rate"] = 98.5  // 模拟成功率
	stats["average_time"] = 125.0 // 模拟平均响应时间（毫秒）
	stats["period"] = period

	// 模拟热门端点统计
	topEndpoints := []map[string]interface{}{
		{
			"endpoint": "/api/update",
			"calls":    totalCalls * 40 / 100, // 40%
			"avg_time": 95.0,
		},
		{
			"endpoint": "/api/login",
			"calls":    totalCalls * 25 / 100, // 25%
			"avg_time": 180.0,
		},
		{
			"endpoint": "/api/heartbeat",
			"calls":    totalCalls * 20 / 100, // 20%
			"avg_time": 45.0,
		},
		{
			"endpoint": "/api/announcement",
			"calls":    totalCalls * 15 / 100, // 15%
			"avg_time": 120.0,
		},
	}

	stats["top_endpoints"] = topEndpoints
	return stats, nil
}

// getUpdateRequestCount 获取今日更新请求统计数量
func (s *StatsService) getUpdateRequestCount() int {
	if s.cache == nil {
		return 0
	}

	today := time.Now().Format("2006-01-02")
	cacheKey := fmt.Sprintf("update_request_count:%s", today)

	// 从Redis获取今日更新请求数量
	var updateRequestCount int64
	if err := s.cache.Get(cacheKey, &updateRequestCount); err == nil {
		logger.Debug("今日更新请求统计: %d 次", updateRequestCount)
		return int(updateRequestCount)
	} else {
		logger.Debug("未找到今日更新请求统计数据")
		return 0
	}
}
