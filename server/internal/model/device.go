package model

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// DeviceInfo 设备信息
type DeviceInfo struct {
	CPU         string `json:"cpu"`         // CPU信息
	Memory      string `json:"memory"`      // 内存信息
	Motherboard string `json:"motherboard"` // 主板信息
	Disk        string `json:"disk"`        // 硬盘信息
	MACAddress  string `json:"mac_address"` // MAC地址
	Fingerprint string `json:"fingerprint"` // 设备指纹
	FirstSeen   int64  `json:"first_seen"`  // 首次使用时间（Unix时间戳）
	LastSeen    int64  `json:"last_seen"`   // 最后使用时间（Unix时间戳）
	IsActive    bool   `json:"is_active"`   // 是否活跃
}

// Value 实现 driver.Valuer 接口
func (d DeviceInfo) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan 实现 sql.Scanner 接口
func (d *DeviceInfo) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	return json.Unmarshal(bytes, d)
}

// DeviceHistory 设备历史记录
type DeviceHistory struct {
	ID         uint   `json:"id" gorm:"column:id;primaryKey;autoIncrement"`                            // 主键ID
	Uin        int64  `json:"uin" gorm:"column:uin;not null;uniqueIndex:idx_uin_device"`               // 用户UIN
	Device     string `json:"device" gorm:"column:device;size:64;not null;uniqueIndex:idx_uin_device"` // 设备标识
	LoginTime  int64  `json:"login_time" gorm:"column:login_time;index"`                               // 最后登录时间（Unix时间戳）
	LogoutTime *int64 `json:"logout_time" gorm:"column:logout_time;index"`                             // 登出时间（Unix时间戳，可为空）
	IPAddress  string `json:"ip_address" gorm:"column:ip_address;size:45"`                             // 最后登录IP地址
	Status     string `json:"status" gorm:"column:status;size:20;default:online"`                      // 状态
	FirstSeen  int64  `json:"first_seen" gorm:"column:first_seen"`                                     // 首次登录时间
	LoginCount int    `json:"login_count" gorm:"column:login_count;default:1"`                         // 登录次数
}

// TableName 指定表名
func (DeviceHistory) TableName() string {
	return "device_histories"
}

// 创建新的设备信息
func NewDeviceInfo(cpu, memory, motherboard, disk, macAddress, fingerprint string) *DeviceInfo {
	now := time.Now().Unix()
	return &DeviceInfo{
		CPU:         cpu,
		Memory:      memory,
		Motherboard: motherboard,
		Disk:        disk,
		MACAddress:  macAddress,
		Fingerprint: fingerprint,
		FirstSeen:   now,
		LastSeen:    now,
		IsActive:    true,
	}
}

// 创建新的设备历史记录
func NewDeviceHistory(uin int64, device string, ipAddress string) *DeviceHistory {
	now := time.Now().Unix()
	return &DeviceHistory{
		Uin:        uin,
		Device:     device,
		LoginTime:  now,
		IPAddress:  ipAddress,
		Status:     "online",
		FirstSeen:  now,
		LoginCount: 1,
	}
}
