package model

import (
	"time"
)

// UpdateConfig 更新配置模型
type UpdateConfig struct {
	ID             uint64    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name           string    `gorm:"column:name;size:50;not null;index" json:"name"`                  // 应用名称，如 "bns-helper"
	Version        string    `gorm:"column:version;size:20;not null" json:"version"`                  // 当前版本号
	ExecutablePath string    `gorm:"column:executable_path;size:100;not null" json:"executable_path"` // 可执行文件路径
	URL            string    `gorm:"column:url;size:500;not null" json:"url"`                         // 下载链接
	Checksum       string    `gorm:"column:checksum;size:64" json:"checksum"`                         // 文件校验和（SHA256）
	PluginVersion  string    `gorm:"column:plugin_version;size:20" json:"plugin_version"`             // 插件版本号
	PluginURL      string    `gorm:"column:plugin_url;size:500" json:"plugin_url"`                    // 插件下载链接
	Beta           bool      `gorm:"column:beta;default:0" json:"beta"`                               // 是否为测试版本
	Force          bool      `gorm:"column:force;default:0" json:"force"`                             // 是否强制更新（心跳中强制关闭应用）
	IsActive       bool      `gorm:"column:is_active;default:1" json:"is_active"`                     // 是否启用
	CreatedAt      time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt      time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// WhitelistGroup 白名单群组模型
type WhitelistGroup struct {
	ID          uint64    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	GroupID     int64     `gorm:"column:group_id;not null;unique" json:"group_id"` // 群组ID
	IsActive    bool      `gorm:"column:is_active;default:1" json:"is_active"`     // 是否启用
	Description string    `gorm:"column:description;size:200" json:"description"`  // 描述信息
	CreatedAt   time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (UpdateConfig) TableName() string {
	return "update_config"
}

// TableName 指定表名
func (WhitelistGroup) TableName() string {
	return "bns_whitelist_group"
}
