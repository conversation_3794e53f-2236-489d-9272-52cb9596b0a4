package model

import (
	"time"
)

// 口令码模型
type CDkey struct {
	ID         uint       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	CDkey      string     `gorm:"column:cdkey;size:255;not null;uniqueIndex" json:"cdkey"` // CDKey
	Type       string     `gorm:"column:type;size:50;not null;index" json:"type"`          // 类型
	MaxNum     uint       `gorm:"column:MaxNum;default:0" json:"max_num"`                  // 可激活的最大数量
	RemainNum  uint       `gorm:"column:RemainNum;default:0" json:"remain_num"`            // 可激活的剩余数量
	Reason     string     `gorm:"column:reason;size:255" json:"reason"`                    // 原因
	StartTime  *time.Time `gorm:"column:startTime" json:"start_time"`                      // 激活开始时间
	EndTime    *time.Time `gorm:"column:endTime" json:"end_time"`                          // 激活结束时间
	UpdateTime time.Time  `gorm:"column:updateTime;autoUpdateTime" json:"update_time"`     // 最后更新时间
	Batch      *int       `gorm:"column:batch" json:"batch"`                               // 批次
	Admin      *int       `gorm:"column:admin" json:"admin"`                               // 生成管理员
	Permission uint8      `gorm:"column:permission;default:1" json:"permission"`           // 权限等级
}

// TableName 指定表名
func (CDkey) TableName() string {
	return "bns_cdkey"
}

// 时间类型口令码模型
type CDKeyCustomize struct {
	CDkey      string     `gorm:"column:cdkey;size:255;primaryKey" json:"cdkey"`                                              // CDKey (主键)
	TimeType   string     `gorm:"column:timeType;type:enum('fixed','duration','usabletimes');not null" json:"time_type"`      // 时间类型：duration-持续时间，fixed-固定时间
	Fixed      *time.Time `gorm:"column:fixed" json:"fixed"`                                                                  // 固定过期时间
	Duration   int        `gorm:"column:duration;default:0" json:"duration"`                                                  // 持续天数
	CreateTime time.Time  `gorm:"column:createTime;default:CURRENT_TIMESTAMP" json:"create_time"`                             // 数据创建时间
	UpdateTime time.Time  `gorm:"column:updateTime;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"update_time"` // 最后更新时间
}

// TableName 指定表名
func (CDKeyCustomize) TableName() string {
	return "bns_cdkey_customize"
}

// 抽奖次数类型口令码模型
type CDKeyDrawtimes struct {
	CDkey      string    `gorm:"column:cdkey;size:255;primaryKey" json:"cdkey"`                                               // CDKey (主键)
	Schedule   *int      `gorm:"column:schedule" json:"schedule"`                                                             // 计划
	Number     int       `gorm:"column:number;default:0" json:"number"`                                                       // 抽奖次数
	CreateTime time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP" json:"create_time"`                             // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"update_time"` // 更新时间
}

// TableName 指定表名
func (CDKeyDrawtimes) TableName() string {
	return "bns_cdkey_drawtimes"
}

// 检查口令码是否已使用完毕
func (c *CDkey) IsUsed() bool {
	return c.RemainNum <= 0
}

// 检查口令码是否在有效期内
func (c *CDkey) IsActive() bool {
	now := time.Now()

	// 检查开始时间
	if c.StartTime != nil && now.Before(*c.StartTime) {
		return false
	}

	// 检查结束时间
	if c.EndTime != nil && now.After(*c.EndTime) {
		return false
	}

	return true
}

// ==================== 通信结构体 ==================== //

// 口令码激活请求消息体结构
type CDKeyActivateRequest struct {
	Token string // Token
	CDKey string // 口令码
}

// 口令码激活响应消息体结构
type CDKeyActivateResponse struct {
	ErrorCode            uint32 // 错误码
	ErrorMsg             string // 错误消息（仅在ErrorCode != 0时包含）
	Message              string // 激活结果消息
	Permission           *uint8 // 更新后的权限级别（仅在权限有变化时包含）
	PermissionExpiration *int64 // 更新后的权限过期时间（仅在权限有变化时包含）：-1=永久，0=无权限，>0=具体时间戳
}

// 口令码信息结构
type CDKeyInfo struct {
	CDKey      string     `gorm:"column:cdkey"`
	Type       string     `gorm:"column:type"`
	TimeType   string     `gorm:"column:timeType"`
	Fixed      *time.Time `gorm:"column:fixed"`
	Duration   int        `gorm:"column:duration"`
	Permission uint8      `gorm:"column:permission"`
}

// CDKeyPermissionInfo CDKey权限信息结构
type CDKeyPermissionInfo struct {
	CDKey      string
	Permission uint8
	StartTime  int64
	EndTime    int64
	TimeType   string // 时间类型：duration 或 fixed
	Fixed      *time.Time
	Duration   int
}

// PermissionResult 权限计算结果
type PermissionResult struct {
	Level      uint8 // 权限等级：0=普通用户，1=高级用户，2=超级用户，3=特级用户
	Expiration int64 // 权限过期时间：0=无权限，-1=永久权限，>0=具体时间戳
}
