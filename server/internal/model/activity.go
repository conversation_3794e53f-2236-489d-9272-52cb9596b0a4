package model

import (
	"encoding/json"
	"time"
)

// ==================== 基本模型 ==================== //

// 活动模型
type Activity struct {
	ActivityId   uint64    `gorm:"column:activity_id;primaryKey" json:"activity_id"`                        // 活动ID（主键，ulong）
	ActivityName string    `gorm:"column:activity_name;size:200;not null" json:"activity_name"`             // 活动名称
	ActivityUrl  string    `gorm:"column:activity_url;size:200" json:"activity_url"`                        // 活动链接
	ServiceType  byte      `gorm:"column:service_type;type:tinyint;not null;default:0" json:"service_type"` // 服务类型 0=bns 1=ztx 2=zncg
	Priority     byte      `gorm:"column:priority;type:tinyint;not null;default:0" json:"priority"`         // 优先级
	Version      uint16    `gorm:"column:version;type:smallint;not null;default:1" json:"version"`          // 活动版本号
	Status       byte      `gorm:"column:status;type:tinyint;not null;default:0" json:"status"`             // 状态：0-草稿，1-活跃，2-已结束
	BeginTime    time.Time `gorm:"column:begin_time;not null" json:"begin_time"`                            // 开始时间
	EndTime      time.Time `gorm:"column:end_time;not null" json:"end_time"`                                // 结束时间

	// 关联的流程
	Flows []ActivityFlow `json:"flows"`
}

// 设置表名
func (Activity) TableName() string {
	return "bns_activity"
}

// 活动流程模型
type ActivityFlow struct {
	ID         uint64 `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	ActivityID uint64 `gorm:"column:activity_id;not null;index" json:"activity_id"`        // 关联的活动ID
	FlowId     uint64 `gorm:"column:flow_id;not null" json:"flow_id"`                      // 流程ID
	FlowName   string `gorm:"column:flow_name;size:200;not null" json:"flow_name"`         // 流程名称
	Group      uint32 `gorm:"column:group" json:"group"`                                   // 流程组，存在组的流程都需要手动领取
	IdeToken   string `gorm:"column:ide_token;size:100;not null" json:"ide_token"`         // IDE令牌
	TplType    string `gorm:"column:tpl_type;size:100;default:default" json:"tpl_type"`    // 模板类型
	Parameters string `gorm:"column:parameters;type:text" json:"parameters"`               // 参数配置（JSON格式）
	Status     byte   `gorm:"column:status;type:tinyint;not null;default:1" json:"status"` // 状态：0-禁用，1-启用
	SortOrder  int    `gorm:"column:sort_order;not null;default:0" json:"sort_order"`      // 排序

	// 外键信息
	GroupInfo *ActivityGroup `gorm:"foreignKey:Group" json:"group_info,omitempty"` // 分组信息
}

// 设置表名
func (ActivityFlow) TableName() string {
	return "bns_activity_flow"
}

// 活动分组模型
type ActivityGroup struct {
	Group uint32 `gorm:"primaryKey;column:group" json:"group"` // 分组ID（主键）
	Text  string `gorm:"column:text" json:"text"`              // 分组说明
}

// 设置表名
func (ActivityGroup) TableName() string {
	return "bns_activity_group"
}

// 活动流程参数结构
type ActivityFlowParameter struct {
	Key   string // 参数键
	Desc  string // 参数描述
	Value string // 设定的参数值
}

// 解析Parameters字符串为结构化数据
func (af *ActivityFlow) GetParameters() []ActivityFlowParameter {
	// 尝试解析为参数对象格式
	var paramMap map[string]interface{}
	err := json.Unmarshal([]byte(af.Parameters), &paramMap)
	if err != nil {
		return []ActivityFlowParameter{}
	}

	var params []ActivityFlowParameter
	for key, value := range paramMap {
		param := ActivityFlowParameter{
			Key:  key,
			Desc: key,
		}

		// 尝试解析详细信息
		if paramObj, ok := value.(map[string]interface{}); ok {
			if desc, exists := paramObj["desc"]; exists {
				if nameStr, ok := desc.(string); ok {
					param.Desc = nameStr
				}
			}
			if value, exists := paramObj["value"]; exists {
				if valueStr, ok := value.(string); ok {
					param.Value = valueStr
				}
			}
		} else {
			// 如果值是简单类型，直接作为值
			if valueStr, ok := value.(string); ok {
				param.Value = valueStr
			}
		}

		params = append(params, param)
	}

	return params
}
