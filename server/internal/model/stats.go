package model

import (
	"time"
)

// 在线用户统计历史记录
type OnlineStatsHistory struct {
	ID                 uint   `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	OnlineCount        int    `json:"online_count" gorm:"column:online_count;not null"`        // 在线用户数
	AuthActiveUsers    int    `json:"auth_active_users" gorm:"column:auth_active_users"`       // 认证活跃用户数
	AuthTotalTokens    int    `json:"auth_total_tokens" gorm:"column:auth_total_tokens"`       // 认证总token数
	HeartbeatActive    uint32 `json:"heartbeat_active" gorm:"column:heartbeat_active"`         // 心跳活跃设备数
	HeartbeatTotal     int    `json:"heartbeat_total" gorm:"column:heartbeat_total"`           // 心跳总设备数
	UpdateRequestCount int    `json:"update_request_count" gorm:"column:update_request_count"` // 更新请求数量
	Timestamp          int64  `json:"timestamp" gorm:"column:timestamp;not null;index"`        // 记录时间戳
}

// 指定表名
func (OnlineStatsHistory) TableName() string {
	return "online_stats_history"
}

// 获取记录时间
func (o *OnlineStatsHistory) GetTimestamp() time.Time {
	return time.Unix(o.Timestamp, 0)
}

// 设置记录时间
func (o *OnlineStatsHistory) SetTimestamp(t time.Time) {
	o.Timestamp = t.Unix()
}
