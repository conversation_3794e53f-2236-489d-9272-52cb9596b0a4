package database

import (
	"fmt"
	"sync"
	"time"

	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	DB   *gorm.DB
	once sync.Once
	mu   sync.RWMutex
)

// InitDB 初始化数据库连接
func InitDB() error {
	var initErr error
	once.Do(func() {
		initErr = initDatabase()
	})
	return initErr
}

// initDatabase 实际的数据库初始化逻辑
func initDatabase() error {
	dsn := buildDSN()

	var err error
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger:                                   logger.Default.LogMode(logger.Info),
		DisableForeignKeyConstraintWhenMigrating: true, // 迁移时禁用外键约束
	})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %v", err)
	}

	// 配置连接池
	if err := configureConnectionPool(); err != nil {
		return fmt.Errorf("failed to configure connection pool: %v", err)
	}

	// 执行数据库迁移
	if err := AutoMigrate(); err != nil {
		return fmt.Errorf("failed to migrate database: %v", err)
	}

	return nil
}

// buildDSN 构建数据库连接字符串
func buildDSN() string {
	// 添加更多连接参数以提高稳定性
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%v&loc=%s&sql_mode=''&timeout=%s&readTimeout=%s&writeTimeout=%s&interpolateParams=true",
		viper.GetString("database.username"),
		viper.GetString("database.password"),
		viper.GetString("database.host"),
		viper.GetInt("database.port"),
		viper.GetString("database.dbname"),
		viper.GetString("database.charset"),
		viper.GetBool("database.parseTime"),
		viper.GetString("database.loc"),
		viper.GetString("database.timeout"),
		viper.GetString("database.readTimeout"),
		viper.GetString("database.writeTimeout"),
	)
}

// configureConnectionPool 配置数据库连接池
func configureConnectionPool() error {
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get database instance: %v", err)
	}

	// 设置连接池参数
	maxIdleConns := viper.GetInt("database.maxIdleConns")
	maxOpenConns := viper.GetInt("database.maxOpenConns")
	connMaxLifetime := time.Duration(viper.GetInt("database.connMaxLifetime")) * time.Second
	connMaxIdleTime := time.Duration(viper.GetInt("database.connMaxIdleTime")) * time.Second

	sqlDB.SetMaxIdleConns(maxIdleConns)
	sqlDB.SetMaxOpenConns(maxOpenConns)
	sqlDB.SetConnMaxLifetime(connMaxLifetime)
	sqlDB.SetConnMaxIdleTime(connMaxIdleTime)

	return nil
}

// GetDB 获取数据库连接（带连接检查和重连机制）
func GetDB() *gorm.DB {
	mu.RLock()
	db := DB
	mu.RUnlock()

	// 检查连接是否有效
	if db != nil {
		sqlDB, err := db.DB()
		if err == nil {
			if err := sqlDB.Ping(); err == nil {
				return db
			}
		}
	}

	// 连接无效，尝试重连
	if err := reconnectDatabase(); err != nil {
		// 重连失败，返回原连接（可能为nil）
		return db
	}

	mu.RLock()
	defer mu.RUnlock()
	return DB
}

// reconnectDatabase 重新连接数据库
func reconnectDatabase() error {
	mu.Lock()
	defer mu.Unlock()

	dsn := buildDSN()

	newDB, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger:                                   logger.Default.LogMode(logger.Info),
		DisableForeignKeyConstraintWhenMigrating: true,
	})
	if err != nil {
		return fmt.Errorf("failed to reconnect to database: %v", err)
	}

	// 配置新连接的连接池
	sqlDB, err := newDB.DB()
	if err != nil {
		return fmt.Errorf("failed to get database instance: %v", err)
	}

	maxIdleConns := viper.GetInt("database.maxIdleConns")
	maxOpenConns := viper.GetInt("database.maxOpenConns")
	connMaxLifetime := time.Duration(viper.GetInt("database.connMaxLifetime")) * time.Second
	connMaxIdleTime := time.Duration(viper.GetInt("database.connMaxIdleTime")) * time.Second

	sqlDB.SetMaxIdleConns(maxIdleConns)
	sqlDB.SetMaxOpenConns(maxOpenConns)
	sqlDB.SetConnMaxLifetime(connMaxLifetime)
	sqlDB.SetConnMaxIdleTime(connMaxIdleTime)

	// 关闭旧连接
	if DB != nil {
		if oldSqlDB, err := DB.DB(); err == nil {
			oldSqlDB.Close()
		}
	}

	DB = newDB
	return nil
}

// HealthCheck 检查数据库连接健康状态
func HealthCheck() error {
	db := GetDB()
	if db == nil {
		return fmt.Errorf("database connection is nil")
	}

	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %v", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("database ping failed: %v", err)
	}

	return nil
}

// InitWithDSN 使用DSN字符串初始化数据库连接（用于调试）
func InitWithDSN(dsn string) error {
	var err error
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})

	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// 配置连接池
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	return nil
}
