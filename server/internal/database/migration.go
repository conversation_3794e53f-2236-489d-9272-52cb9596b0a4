package database

import (
	"fmt"
	gatewayModel "udp-server/server/internal/gateway/model"
	"udp-server/server/internal/model"
)

// AutoMigrate 自动执行数据库迁移
func AutoMigrate() error {
	// 先执行SQL命令禁用外键检查
	DB.Exec("SET FOREIGN_KEY_CHECKS = 0")
	defer DB.Exec("SET FOREIGN_KEY_CHECKS = 1")

	// 检查表是否存在，如果不存在则创建
	migrator := DB.Migrator()

	// 按照依赖关系顺序创建表
	tables := []interface{}{
		&model.User{},
		&model.DeviceHistory{},
		&model.Lucky{},
		&model.UserDraw{},
		&model.LuckyReward{},
		&model.CDkey{},
		&model.CDKeyCustomize{}, // CDKey自定义配置表
		&model.UserDrawResult{},
		&model.OnlineStatsHistory{},
		&model.RiskEvent{},         // 风控事件表
		&model.RiskControlConfig{}, // 风控配置表
		&gatewayModel.AdminItem{},  // 管理员权限项表
		&gatewayModel.AdminLog{},   // 管理员操作日志表
		&model.Announcement{},      // 公告表
		&model.UpdateConfig{},      // 更新配置表
		&model.WhitelistGroup{},    // 白名单群组表
		&model.Activity{},          // 活动表
		&model.ActivityFlow{},      // 活动流程表
		&model.ActivityGroup{},     // 活动分组表
	}

	// 只创建不存在的表，跳过已存在的表以避免索引冲突
	for _, table := range tables {
		if !migrator.HasTable(table) {
			fmt.Printf("Creating table for %T\n", table)
			if err := migrator.CreateTable(table); err != nil {
				return fmt.Errorf("failed to create table %T: %v", table, err)
			}
		} else {
			fmt.Printf("Table for %T already exists, skipping migration\n", table)
		}
	}

	fmt.Println("数据库迁移完成")
	return nil
}
