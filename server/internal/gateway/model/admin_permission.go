package model

// AdminItem 权限ID到功能的映射表
type AdminItem struct {
	ID         uint64 `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Permission string `gorm:"column:permission;size:50;not null" json:"permission"` // 权限标识
	Name       string `gorm:"column:name;size:100;not null" json:"name"`            // 权限名称
}

// TableName 指定表名
func (AdminItem) TableName() string {
	return "bns_useradminitem"
}

// GetDefaultAdminItems 获取默认权限项定义
func GetDefaultAdminItems() []AdminItem {
	return []AdminItem{
		{ID: 1, Permission: "system.dashboard", Name: "系统仪表板"},
		{ID: 2, Permission: "user.view", Name: "查看用户"},
		{ID: 3, Permission: "user.edit", Name: "编辑用户"},
		{ID: 4, Permission: "announcement.view", Name: "查看公告"},
		{ID: 5, Permission: "announcement.create", Name: "创建公告"},
		{ID: 6, Permission: "announcement.edit", Name: "编辑公告"},
		{ID: 7, Permission: "announcement.delete", Name: "删除公告"},
		{ID: 8, Permission: "cdkey.view", Name: "查看口令码"},
		{ID: 9, Permission: "cdkey.create", Name: "创建口令码"},
		{ID: 10, Permission: "cdkey.edit", Name: "编辑口令码"},
		{ID: 11, Permission: "risk.view", Name: "查看风控"},
		{ID: 12, Permission: "risk.process", Name: "处理风控"},
		{ID: 13, Permission: "risk.config", Name: "风控配置"},
		{ID: 14, Permission: "system.update", Name: "更新管理"},
		{ID: 15, Permission: "admin.view", Name: "查看管理员"},
		{ID: 16, Permission: "admin.create", Name: "创建管理员"},
		{ID: 17, Permission: "admin.edit", Name: "编辑管理员"},
		{ID: 18, Permission: "admin.delete", Name: "删除管理员"},
		{ID: 19, Permission: "system.config", Name: "系统配置"},
	}
}

// GetAdminItemByID 根据ID获取权限项
func GetAdminItemByID(id uint64) *AdminItem {
	items := GetDefaultAdminItems()
	for _, item := range items {
		if item.ID == id {
			return &item
		}
	}
	return nil
}

// GetAdminItemByPermission 根据权限键获取权限项
func GetAdminItemByPermission(permission string) *AdminItem {
	items := GetDefaultAdminItems()
	for _, item := range items {
		if item.Permission == permission {
			return &item
		}
	}
	return nil
}
