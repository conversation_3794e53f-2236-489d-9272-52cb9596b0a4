package service

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"udp-server/server/internal/gateway/model"
	gatewayModel "udp-server/server/internal/gateway/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// AdminService 管理后台专用服务
type AdminService struct {
	db    *gorm.DB
	cache cache.Cache
}

// 创建管理后台服务
func NewAdminService(db *gorm.DB, cache cache.Cache) *AdminService {
	return &AdminService{
		db:    db,
		cache: cache,
	}
}

// 生成管理员令牌
func (s *AdminService) GenerateAdminToken(adminID uint64) (string, error) {
	// 生成32字节随机令牌
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	token := hex.EncodeToString(bytes)

	// 存储到缓存，有效期24小时
	cacheKey := fmt.Sprintf("admin_token:%s", token)
	if err := s.cache.Set(cacheKey, adminID, 24*time.Hour); err != nil {
		return "", err
	}

	return token, nil
}

// 验证管理员令牌
func (s *AdminService) ValidateAdminToken(token string) (uint64, error) {
	cacheKey := fmt.Sprintf("admin_token:%s", token)
	var adminID uint64
	if err := s.cache.Get(cacheKey, &adminID); err != nil {
		return 0, fmt.Errorf("令牌无效或已过期")
	}

	return adminID, nil
}

// 验证管理员账号
func (s *AdminService) ValidateAdminLogin(username, password string) (*gatewayModel.Admin, error) {
	var admin gatewayModel.Admin

	err := s.db.Table("bns_useradmin").
		Where("username = ? AND isAction = 1", username).
		First(&admin).Error

	if err == gorm.ErrRecordNotFound {
		logger.Warn("管理员用户不存在: username=%s", username)
		return nil, nil
	} else if err != nil {
		logger.Error("查询管理员失败: %v", err)
		return nil, fmt.Errorf("查询管理员失败: %w", err)
	}

	logger.Info("找到管理员: UID=%d, Username=%s, Power=%s, Super=%d", admin.UID, admin.Username, admin.Power, admin.Super)

	// 验证密码
	expectedHash := s.GenerateAdminPassword(password)
	if expectedHash != admin.Password {
		logger.Warn("管理员密码验证失败: username=%s", username)
		return nil, fmt.Errorf("密码验证失败")
	}

	return &admin, nil
}

// 为管理员生成新密码哈希
func (s *AdminService) GenerateAdminPassword(password string) string {
	// 使用固定盐值，实际生产环境应该使用随机盐值
	salt := "bnszs_salt_2024"

	// 组合密码和盐值
	combined := password + salt

	// 计算SHA256哈希
	hash := sha256.Sum256([]byte(combined))

	// 返回十六进制字符串
	return hex.EncodeToString(hash[:])
}

// 获取管理员列表
func (s *AdminService) GetAdminList() ([]map[string]interface{}, error) {
	var admins []struct {
		UID      uint64 `gorm:"column:uid"`
		Username string `gorm:"column:username"`
		Power    string `gorm:"column:power"`
		IsAction int    `gorm:"column:isAction"`
		Super    int    `gorm:"column:super"`
	}

	err := s.db.Table("bns_useradmin").
		Select("uid, username, power, isAction, super").
		Find(&admins).Error

	if err != nil {
		return nil, fmt.Errorf("查询管理员列表失败: %v", err)
	}

	result := make([]map[string]interface{}, len(admins))
	for i, admin := range admins {
		result[i] = map[string]interface{}{
			"uid":       admin.UID,
			"username":  admin.Username,
			"power":     admin.Power,
			"is_action": admin.IsAction,
			"is_super":  admin.Super == 1,
		}
	}

	return result, nil
}

// 创建管理员
func (s *AdminService) CreateAdmin(username, password, power string, isSuper bool) (uint64, error) {
	// 检查用户名是否已存在
	var count int64
	err := s.db.Table("bns_useradmin").
		Where("username = ?", username).
		Count(&count).Error

	if err != nil {
		return 0, fmt.Errorf("检查用户名失败: %v", err)
	}

	if count > 0 {
		return 0, fmt.Errorf("用户名已存在")
	}

	// 如果没有指定权限，设置默认权限
	if power == "" {
		if isSuper {
			power = "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20"
		} else {
			power = "1,2,3,4,5"
		}
	}

	// 加密密码
	hashedPassword := s.GenerateAdminPassword(password)

	// 插入新管理员
	admin := map[string]interface{}{
		"username": username,
		"password": hashedPassword,
		"power":    power,
		"isAction": 1,
	}

	result := s.db.Table("bns_useradmin").Create(&admin)
	if result.Error != nil {
		return 0, fmt.Errorf("创建管理员失败: %v", result.Error)
	}

	// 获取插入的ID
	var newAdmin struct {
		UID uint64 `gorm:"column:uid"`
	}
	err = s.db.Table("bns_useradmin").
		Where("username = ?", username).
		Select("uid").
		First(&newAdmin).Error

	if err != nil {
		return 0, fmt.Errorf("获取新管理员ID失败: %v", err)
	}

	return newAdmin.UID, nil
}

// 创建管理员操作日志
func (s *AdminService) CreateAdminLog(log *gatewayModel.AdminLog) error {
	return s.db.Create(log).Error
}

// 更新管理员信息
func (s *AdminService) UpdateAdmin(adminID string, username, password string, isAction int) error {
	updateData := map[string]interface{}{
		"username": username,
		"isAction": isAction,
	}

	// 如果提供了新密码，则更新密码
	if password != "" {
		updateData["password"] = s.GenerateAdminPassword(password)
	}

	err := s.db.Table("bns_useradmin").
		Where("uid = ?", adminID).
		Updates(updateData).Error

	if err != nil {
		return fmt.Errorf("更新管理员基本信息失败: %v", err)
	}

	return nil
}

// 删除管理员
func (s *AdminService) DeleteAdmin(adminID string) error {
	err := s.db.Table("bns_useradmin").
		Where("uid = ?", adminID).
		Update("isAction", 0).Error

	if err != nil {
		return fmt.Errorf("删除管理员失败: %v", err)
	}

	return nil
}

// 获取特定管理员详情
func (s *AdminService) GetAdminByID(adminID string) (*model.Admin, error) {
	var admin model.Admin
	err := s.db.Table("bns_useradmin").
		Select("uid, username, power, isAction, super").
		Where("uid = ?", adminID).
		First(&admin).Error

	if err != nil {
		return nil, fmt.Errorf("查询管理员详情失败: %v", err)
	}

	return &admin, nil
}
