package service

import (
	"fmt"
	"math/rand"
	"time"

	"udp-server/server/internal/database"
	"udp-server/server/internal/model"
	"udp-server/server/pkg/logger"
	"udp-server/server/pkg/utils"

	"gorm.io/gorm"
)

// 口令码管理后台服务
type CDKeyAdminService struct {
	db *gorm.DB
}

func NewCDKeyAdminService() *CDKeyAdminService {
	return &CDKeyAdminService{
		db: database.GetDB(),
	}
}

// 获取口令码列表
func (s *CDKeyAdminService) GetCDKeys(page, limit int, searchType, searchStatus, searchKeyword string) ([]map[string]interface{}, error) {
	offset := (page - 1) * limit

	// 构建查询
	query := s.db.Model(&model.CDkey{})

	// 添加搜索条件
	if searchType != "" {
		query = query.Where("type = ?", searchType)
	}

	if searchKeyword != "" {
		query = query.Where("cdkey LIKE ?", "%"+searchKeyword+"%")
	}

	// 状态过滤
	if searchStatus != "" {
		now := time.Now()
		switch searchStatus {
		case "active":
			query = query.Where("RemainNum > 0 AND (startTime IS NULL OR startTime <= ?) AND (endTime IS NULL OR endTime > ?)", now, now)
		case "used":
			query = query.Where("RemainNum <= 0")
		case "expired":
			query = query.Where("endTime IS NOT NULL AND endTime <= ?", now)
		case "unused":
			query = query.Where("RemainNum > 0")
		}
	}

	// 按更新时间倒序排列
	query = query.Order("updateTime DESC")

	var cdkeys []model.CDkey
	err := query.Offset(offset).Limit(limit).Find(&cdkeys).Error
	if err != nil {
		return nil, fmt.Errorf("获取CDKEY列表失败: %v", err)
	}

	result := make([]map[string]interface{}, len(cdkeys))
	for i, cdkey := range cdkeys {
		// 生成描述信息
		var describe string

		if cdkey.Type == "drawtimes" {
			var drawtimes model.CDKeyDrawtimes
			if err := s.db.Where("cdkey = ?", cdkey.CDkey).First(&drawtimes).Error; err == nil {
				describe = fmt.Sprintf("抽奖%d次", drawtimes.Number)
			}
		} else {
			var customize model.CDKeyCustomize
			if err := s.db.Where("cdkey = ?", cdkey.CDkey).First(&customize).Error; err == nil {
				timeType := customize.TimeType

				if timeType == "duration" {
					describe = fmt.Sprintf("%d天", customize.Duration)
				} else if timeType == "fixed" {
					describe = utils.FormatTime(customize.Fixed)
				}
			}
		}

		result[i] = map[string]interface{}{
			"id":          cdkey.ID,
			"cdkey":       cdkey.CDkey,
			"type":        cdkey.Type,
			"max_num":     cdkey.MaxNum,
			"remain_num":  cdkey.RemainNum,
			"reason":      cdkey.Reason,
			"start_time":  utils.FormatTime(cdkey.StartTime),
			"end_time":    utils.FormatTime(cdkey.EndTime),
			"update_time": utils.FormatTime(&cdkey.UpdateTime),
			"batch":       cdkey.Batch,
			"admin":       cdkey.Admin,
			"describe":    describe,
		}
	}

	return result, nil
}

// 获取指定口令码的使用记录
func (s *CDKeyAdminService) GetCDKeyUsageRecords(cdkeyStr string) (map[string]interface{}, error) {
	// 先查询CDKey信息获取管理员信息
	var cdkey model.CDkey
	err := s.db.Where("cdkey = ?", cdkeyStr).First(&cdkey).Error
	if err != nil {
		return nil, fmt.Errorf("查询口令码信息失败: %v", err)
	}

	// 查询管理员信息
	var adminName string
	if cdkey.Admin != nil && *cdkey.Admin > 0 {
		var admin struct {
			Username string `gorm:"column:username"`
		}
		err := s.db.Table("bns_useradmin").Where("uid = ?", *cdkey.Admin).First(&admin).Error
		if err == nil {
			adminName = admin.Username
		} else {
			adminName = "未知管理员"
		}
	} else {
		adminName = "系统"
	}

	// 先查询总的使用数量
	var totalCount int64
	err = s.db.Model(&model.UserLog{}).
		Where("type = ? AND extra = ?", "cdkey", cdkeyStr).
		Count(&totalCount).Error
	if err != nil {
		return nil, fmt.Errorf("查询口令码使用用户数量失败: %v", err)
	}

	// 查询使用该口令码的用户日志，限制最近50条
	var logs []model.UserLog
	err = s.db.Where("type = ? AND extra = ?", "cdkey", cdkeyStr).
		Order("id DESC").Limit(50).Find(&logs).Error
	if err != nil {
		return nil, fmt.Errorf("查询口令码使用记录失败: %v", err)
	}

	if len(logs) == 0 {
		return map[string]interface{}{
			"admin_name":  adminName,
			"total_count": totalCount,
			"records":     []map[string]interface{}{},
		}, nil
	}

	// 获取所有相关用户的信息
	var userIDs []uint64
	for _, log := range logs {
		userIDs = append(userIDs, log.UID)
	}

	var users []model.User
	err = s.db.Where("uid IN ?", userIDs).Find(&users).Error
	if err != nil {
		return nil, fmt.Errorf("查询用户信息失败: %v", err)
	}

	// 创建用户ID到用户信息的映射
	userMap := make(map[uint64]model.User)
	for _, user := range users {
		userMap[user.UID] = user
	}

	// 构建结果
	result := make([]map[string]interface{}, len(logs))
	for i, log := range logs {
		user, exists := userMap[log.UID]
		var qqNumber uint64
		var userName string
		if exists {
			qqNumber = user.Uin
			userName = user.Name
		} else {
			qqNumber = 0
			userName = "未知用户"
		}

		result[i] = map[string]interface{}{
			"uid":      log.UID,
			"qq":       qqNumber,
			"username": userName,
			"use_time": log.Time,
		}
	}

	return map[string]interface{}{
		"admin_name":  adminName,
		"total_count": totalCount,
		"records":     result,
	}, nil
}

// 获取指定用户的口令码使用记录
func (s *CDKeyAdminService) GetUserCDKeyRecords(uid uint64) ([]map[string]interface{}, error) {
	var logs []model.UserLog
	err := s.db.Where("uid = ? AND type = ?", uid, "cdkey").
		Order("id DESC").Find(&logs).Error
	if err != nil {
		return nil, fmt.Errorf("查询用户口令码使用记录失败: %v", err)
	}

	if len(logs) == 0 {
		return []map[string]interface{}{}, nil
	}

	// 获取所有相关CDKey的信息
	var cdkeyStrs []string
	for _, log := range logs {
		cdkeyStrs = append(cdkeyStrs, log.Extra)
	}

	var cdkeys []model.CDkey
	err = s.db.Where("cdkey IN ?", cdkeyStrs).Find(&cdkeys).Error
	if err != nil {
		return nil, fmt.Errorf("查询CDKey信息失败: %v", err)
	}

	// 创建CDKey到信息的映射
	cdkeyMap := make(map[string]model.CDkey)
	for _, cdkey := range cdkeys {
		cdkeyMap[cdkey.CDkey] = cdkey
	}

	// 构建结果
	result := make([]map[string]interface{}, len(logs))
	for i, log := range logs {
		cdkey, exists := cdkeyMap[log.Extra]
		var cdkeyType string
		var cdkeyReason string
		if exists {
			cdkeyType = cdkey.Type
			cdkeyReason = cdkey.Reason
		} else {
			cdkeyType = "未知"
			cdkeyReason = "CDKey已删除"
		}

		result[i] = map[string]interface{}{
			"cdkey":    log.Extra,
			"type":     cdkeyType,
			"reason":   cdkeyReason,
			"use_time": log.Time,
		}
	}

	return result, nil
}

// 批量创建口令码
func (s *CDKeyAdminService) CreateCDKeysForAdmin(count int, cdkeyType, timeType, reason, prefix string, startTime, endTime *time.Time, adminID int, duration int, fixedTime *time.Time) ([]string, error) {
	if count <= 0 || count > 5000 {
		return nil, fmt.Errorf("CDKEY数量必须在1-5000之间")
	}

	var generatedCDKeys []string

	// 在事务中批量创建
	err := s.db.Transaction(func(tx *gorm.DB) error {
		for i := 0; i < count; i++ {
			// 生成唯一的CDKEY
			cdkeyStr, err := s.generateUniqueCDKey(tx, prefix)
			if err != nil {
				return fmt.Errorf("生成CDKEY失败: %v", err)
			}

			// 创建CDKEY记录
			cdkey := model.CDkey{
				CDkey:      cdkeyStr,
				Type:       cdkeyType,
				MaxNum:     1, // 默认可使用1次
				RemainNum:  1,
				Reason:     reason,
				StartTime:  startTime,
				EndTime:    endTime,
				Admin:      &adminID,
				Permission: 1, // 默认权限等级为1（基础权限）
			}

			if err := tx.Create(&cdkey).Error; err != nil {
				return fmt.Errorf("创建CDKEY记录失败: %v", err)
			}

			// 创建CDKey自定义配置记录
			customize := model.CDKeyCustomize{
				CDkey:    cdkeyStr,
				TimeType: timeType,
				Fixed:    fixedTime,
				Duration: duration,
			}

			if err := tx.Create(&customize).Error; err != nil {
				return fmt.Errorf("创建CDKEY配置记录失败: %v", err)
			}

			generatedCDKeys = append(generatedCDKeys, cdkeyStr)
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	logger.Info("批量创建CDKEY成功: 数量=%d, 类型=%s, 管理员ID=%d", count, cdkeyType, adminID)
	return generatedCDKeys, nil
}

// 根据参数创建CDKey
func (s *CDKeyAdminService) CreateCDKeys(count, cdkeyType, rewardType, rewardValue int, description string, validDays int) ([]model.CDkey, error) {
	// TODO: 实现根据参数创建CDKey的逻辑
	logger.Info("创建CDKey: count=%d, type=%d, rewardType=%d, rewardValue=%d, description=%s, validDays=%d",
		count, cdkeyType, rewardType, rewardValue, description, validDays)

	// 暂时返回空的CDKey列表
	cdkeys := make([]model.CDkey, count)
	for i := 0; i < count; i++ {
		cdkeys[i] = model.CDkey{
			CDkey:     fmt.Sprintf("MOCK_%d_%d", cdkeyType, i+1),
			Type:      fmt.Sprintf("type_%d", cdkeyType),
			MaxNum:    uint(rewardValue),
			RemainNum: uint(rewardValue),
			Reason:    description,
		}
	}

	return cdkeys, nil
}

// 绑定CDKey到用户
func (s *CDKeyAdminService) BindCDKeyToUser(cdkeyCode string, userID uint64) error {
	// TODO: 实现CDKey绑定逻辑
	logger.Info("绑定CDKey %s 到用户 %d", cdkeyCode, userID)
	return nil
}

// 删除CDKey
func (s *CDKeyAdminService) DeleteCDKey(id uint64) error {
	err := s.db.Delete(&model.CDkey{}, id).Error
	if err != nil {
		logger.Error("删除CDKey失败: %v", err)
		return err
	}
	return nil
}

// 生成唯一的CDKEY
func (s *CDKeyAdminService) generateUniqueCDKey(tx *gorm.DB, prefix string) (string, error) {
	const maxRetries = 10

	for i := 0; i < maxRetries; i++ {
		// 生成随机字符串
		randomStr := s.generateRandomString(8)
		cdkey := prefix + randomStr

		// 检查是否已存在
		var count int64
		err := tx.Model(&model.CDkey{}).Where("cdkey = ?", cdkey).Count(&count).Error
		if err != nil {
			return "", err
		}

		if count == 0 {
			return cdkey, nil
		}
	}

	return "", fmt.Errorf("生成唯一CDKEY失败，请重试")
}

// GetCDKeyByID 根据ID获取CDKey
func (s *CDKeyAdminService) GetCDKeyByID(id uint64) (map[string]interface{}, error) {
	var cdkey model.CDkey
	err := s.db.First(&cdkey, id).Error
	if err != nil {
		logger.Error("获取CDKey失败: %v", err)
		return nil, err
	}

	result := map[string]interface{}{
		"id":          cdkey.ID,
		"cdkey":       cdkey.CDkey,
		"type":        cdkey.Type,
		"max_num":     cdkey.MaxNum,
		"remain_num":  cdkey.RemainNum,
		"reason":      cdkey.Reason,
		"update_time": cdkey.UpdateTime.Format("2006-01-02 15:04:05"),
	}

	return result, nil
}

// GetCDKeyStats 获取CDKey统计信息
func (s *CDKeyAdminService) GetCDKeyStats() (map[string]interface{}, error) {
	// TODO: 实现CDKey统计逻辑
	stats := map[string]interface{}{
		"total":   0,
		"active":  0,
		"used":    0,
		"expired": 0,
	}
	return stats, nil
}

// BatchDeleteCDKeys 批量删除CDKey
func (s *CDKeyAdminService) BatchDeleteCDKeys(ids []uint64) (int, error) {
	result := s.db.Delete(&model.CDkey{}, ids)
	if result.Error != nil {
		logger.Error("批量删除CDKey失败: %v", result.Error)
		return 0, result.Error
	}
	return int(result.RowsAffected), nil
}

// 生成指定长度的随机字符串
func (s *CDKeyAdminService) generateRandomString(length int) string {
	const charset = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}
	return string(result)
}
