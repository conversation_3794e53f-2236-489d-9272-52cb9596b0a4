package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"udp-server/server/internal/database"
	"udp-server/server/internal/model"
	coreService "udp-server/server/internal/service"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// 活动管理后台服务
type ActivityAdminService struct {
	activityService *coreService.ActivityService
	cache           cache.Cache
	db              *gorm.DB
}

// 创建活动管理后台服务
func NewActivityAdminService(activityService *coreService.ActivityService, cache cache.Cache) *ActivityAdminService {
	return &ActivityAdminService{
		activityService: activityService,
		cache:           cache,
		db:              database.GetDB(),
	}
}

// ==================== 活动相关方法 ==================== //

// 创建活动
func (s *ActivityAdminService) CreateActivity(ctx context.Context, activity *model.Activity) (*model.Activity, error) {
	err := s.db.WithContext(ctx).Create(activity).Error
	if err != nil {
		logger.Error("创建活动失败: %v", err)
		return nil, err
	}

	return activity, nil
}

// 获取指定活动
func (s *ActivityAdminService) GetActivityById(ctx context.Context, activityId uint64) (*model.Activity, error) {
	var activity model.Activity
	err := s.db.WithContext(ctx).
		Preload("Flows", func(db *gorm.DB) *gorm.DB {
			return db.Order("sort_order ASC")
		}).
		Preload("Flows.GroupInfo").
		First(&activity, activityId).Error
	if err != nil {
		logger.Error("获取活动失败: %v", err)
		return nil, err
	}

	return &activity, nil
}

// 更新活动
func (s *ActivityAdminService) UpdateActivity(ctx context.Context, activityId uint64, req *model.Activity) (*model.Activity, error) {
	// 先查询活动是否存在
	var activity model.Activity
	err := s.db.WithContext(ctx).First(&activity, activityId).Error
	if err != nil {
		logger.Error("活动不存在: %v", err)
		return nil, err
	}

	// 更新字段
	if req.ActivityName != "" {
		activity.ActivityName = req.ActivityName
	}

	activity.ActivityUrl = req.ActivityUrl       // 设置活动链接
	activity.ServiceType = byte(req.ServiceType) // 设置服务器
	activity.Status = byte(req.Status)           // 设置状态
	activity.Priority = byte(req.Priority)       // 设置优先级
	activity.BeginTime = req.BeginTime           // 设置开始时间
	activity.EndTime = req.EndTime               // 设置结束时间
	activity.Version = activity.Version + 1      // 更新版本号

	// 保存到数据库
	err = s.db.WithContext(ctx).Save(&activity).Error
	if err != nil {
		logger.Error("更新活动失败: %v", err)
		return nil, err
	}

	s.activityService.UpdateCache(activity.ActivityId)
	return &activity, nil
}

// 删除活动
func (s *ActivityAdminService) DeleteActivity(ctx context.Context, activityId uint64) error {
	// TODO: 实现删除活动逻辑
	return nil
}

// ==================== 流程相关方法 ==================== //

// 创建流程
func (s *ActivityAdminService) CreateFlow(ctx context.Context, flow *model.ActivityFlow) (*model.ActivityFlow, error) {
	err := s.db.WithContext(ctx).Create(flow).Error
	if err != nil {
		logger.Error("创建流程失败: %v", err)
		return nil, err
	}

	return flow, nil
}

// 获取特定的流程信息
func (s *ActivityAdminService) GetFlowByID(ctx context.Context, id uint64) (*model.ActivityFlow, error) {
	var flow model.ActivityFlow
	err := s.db.WithContext(ctx).First(&flow, id).Error
	if err != nil {
		logger.Error("获取流程失败: %v", err)
		return nil, err
	}

	return &flow, nil
}

// 更新流程
func (s *ActivityAdminService) UpdateFlow(ctx context.Context, flowId uint64, req *model.ActivityFlow) (*model.ActivityFlow, error) {
	// 先查询流程是否存在
	var flow model.ActivityFlow
	err := s.db.WithContext(ctx).First(&flow, flowId).Error
	if err != nil {
		logger.Error("流程不存在: %v", err)
		return nil, err
	}

	// 更新字段
	flow.FlowName = req.FlowName
	flow.Group = req.Group
	flow.IdeToken = req.IdeToken
	flow.Parameters = req.Parameters
	flow.Status = byte(req.Status)
	flow.SortOrder = req.SortOrder

	err = s.db.WithContext(ctx).Save(&flow).Error
	if err != nil {
		logger.Error("更新流程失败: %v", err)
		return nil, err
	}

	s.activityService.UpdateCache(flow.ActivityID)
	return &flow, nil
}

// 只更新流程状态
func (s *ActivityAdminService) UpdateFlowStatus(ctx context.Context, flowId uint64, status int8) (*model.ActivityFlow, error) {
	// 先查询流程是否存在
	var flow model.ActivityFlow
	err := s.db.WithContext(ctx).First(&flow, flowId).Error
	if err != nil {
		logger.Error("流程不存在: %v", err)
		return nil, err
	}

	// 只更新状态字段
	err = s.db.WithContext(ctx).Model(&flow).Update("status", status).Error
	if err != nil {
		logger.Error("更新流程状态失败: %v", err)
		return nil, err
	}

	// 重新查询更新后的流程信息
	err = s.db.WithContext(ctx).First(&flow, flowId).Error
	if err != nil {
		logger.Error("查询更新后的流程失败: %v", err)
		return nil, err
	}

	s.activityService.UpdateCache(flow.ActivityID)
	return &flow, nil
}

// 删除流程
func (s *ActivityAdminService) DeleteFlow(ctx context.Context, flowId uint64) error {
	err := s.db.WithContext(ctx).Delete(&model.ActivityFlow{}, flowId).Error
	if err != nil {
		logger.Error("删除流程失败: %v", err)
		return err
	}

	// TODO: 给当前活动增加版本号
	return nil
}

// 测试流程请求
func (s *ActivityAdminService) TestFlowWithParams(ctx context.Context, flowId uint64, accessToken, openID, appID, accType string, testParams map[string]string) (interface{}, error) {
	// 获取流程信息
	flow, err := s.GetFlowByID(ctx, flowId)
	if err != nil {
		return nil, fmt.Errorf("获取流程信息失败: %w", err)
	}

	// 构建请求参数
	parameters := map[string]string{
		"iChartId":    strconv.FormatUint(flow.FlowId, 10),
		"iSubChartId": strconv.FormatUint(flow.FlowId, 10),
		"sIdeToken":   flow.IdeToken,
		"isPreengage": "1",
		"needGopenid": "1",
	}

	// 添加前端传递的测试参数（前端已经负责参数合并）
	for key, value := range testParams {
		if key != "" && value != "" {
			parameters[key] = value
		}
	}

	// 构建cookies
	cookies := map[string]string{
		"acctype":      accType,
		"openid":       openID,
		"appid":        appID,
		"access_token": accessToken,
	}

	// 发送HTTP请求到AMS服务器
	amsResponse, err := s.SendAMSRequest(ctx, parameters, cookies)
	if err != nil {
		return map[string]interface{}{
			"message": "流程测试失败: " + err.Error(),
			"error":   err.Error(),
		}, nil
	}

	// 直接返回HTTP响应结果
	return amsResponse, nil
}

// 发送AMS请求
func (s *ActivityAdminService) SendAMSRequest(ctx context.Context, parameters map[string]string, cookies map[string]string) (map[string]interface{}, error) {
	// 构建表单数据
	formData := url.Values{}
	for key, value := range parameters {
		formData.Set(key, value)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", "https://comm.ams.game.qq.com/ide/", strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头，参考C#的RequestFlowAsync方法
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("accept", "application/json, text/plain, */*")
	req.Header.Set("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	// 设置cookies，使用分号分隔（与C#一致）
	var cookieStrs []string
	for key, value := range cookies {
		cookieStrs = append(cookieStrs, fmt.Sprintf("%s=%s", key, value))
	}
	if len(cookieStrs) > 0 {
		req.Header.Set("cookie", strings.Join(cookieStrs, ";"))
	}

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		// 如果不是JSON，返回原始文本
		return map[string]interface{}{
			"raw_response": string(body),
			"status_code":  resp.StatusCode,
		}, nil
	}

	result["status_code"] = resp.StatusCode
	return result, nil
}

// ==================== AMS解析方法 ==================== //

// AMS描述文件数据结构
type AMSData struct {
	ActivityID      string                    `json:"iActivityId"`
	ActivityName    string                    `json:"sActivityName"`
	ActivityStatus  string                    `json:"iActivityStatus"`
	ServiceType     string                    `json:"sServiceType"`
	ServiceDept     string                    `json:"sServiceDepartment"`
	ClientType      string                    `json:"sClientType"`
	AccountType     string                    `json:"sAccountType"`
	BeginTimeString string                    `json:"dtBeginTime"`
	EndTimeString   string                    `json:"dtEndTime"`
	OpenTime        string                    `json:"tOpenTime"`
	CloseTime       string                    `json:"tCloseTime"`
	TableNum        string                    `json:"iTableNum"`
	Shutdown        string                    `json:"iShutdown"`
	From            string                    `json:"sFrom"`
	SDID            string                    `json:"sSDID"`
	AMSTrusteeship  interface{}               `json:"sAMSTrusteeship"`
	AmePcUrl        string                    `json:"sAmePcUrl"`
	AmeMobileUrl    string                    `json:"sAmeMobileUrl"`
	ServiceQQAppId  string                    `json:"sServiceQQAppId"`
	IDE             *AMSIde                   `json:"ide"`
	Flows           map[string]*AMSFlowDetail `json:"flows"`
	DefaultTpls     []interface{}             `json:"default_tpls"`
	PaaSId          string                    `json:"iPaaSId"`
	Token           string                    `json:"token"`
	CapAppId        string                    `json:"capAppId"`
	AreaRoleModId   string                    `json:"iAreaRoleModId"`
	Alias           map[string]string         `json:"alias"`
}

// IDE配置
type AMSIde struct {
	RetCode int                 `json:"iRet"`
	Message string              `json:"sMsg"`
	IdeUrl  string              `json:"sIdeUrl"`
	Tokens  map[string]string   `json:"tokens"`
	Flows   map[string]*AMSFlow `json:"flows"`
}

// IDE流程配置
type AMSFlow struct {
	Name           string          `json:"sName"`           // 流程名称
	IdeToken       string          `json:"sIdeToken"`       // IDE令牌
	AccountType    string          `json:"sAccountType"`    // 账号类型
	NeedIBaseUin   string          `json:"bNeedIBaseUin"`   // 是否需要基础UIN
	IsLogin        string          `json:"isLogin"`         // 是否需要登录
	AreaChooseType string          `json:"iAreaChooseType"` // 区域选择类型
	ServiceType    string          `json:"sServiceType"`    // 服务类型
	IdeUrl         string          `json:"sIdeUrl"`         // IDE URL
	TplType        string          `json:"sTplType"`        // 模板类型
	Type           string          `json:"iType"`           // 流程类型
	ResId          string          `json:"iResId"`          // 资源ID
	TargetAppId    string          `json:"targetAppId"`     // 目标应用ID
	AMSTrusteeship string          `json:"sAMSTrusteeship"` // AMS托管
	TargetQQAppId  string          `json:"targetQQAppId"`   // 目标QQ应用ID
	AppName        interface{}     `json:"appName"`         // 应用名称
	GmiFlowId      string          `json:"sGmiFlowId"`      // GMI流程ID
	Captcha        string          `json:"iCaptcha"`        // 验证码
	InputParams    []AMSInputParam `json:"inputParams"`     // 输入参数
	Custom         string          `json:"iCustom"`         // 自定义标识
	AreaService    string          `json:"sAreaService"`    // 区域服务
}

// 输入参数
type AMSInputParam struct {
	Key  string `json:"key"`
	Desc string `json:"desc"`
}

// 流程详情
type AMSFlowDetail struct {
	FlowName    string `json:"sFlowName"`
	MapId       int    `json:"iMapId"`
	AccountType string `json:"sAccountType"`
}

// 获取AMS数据
func (s *ActivityAdminService) FetchAMSData(amsURL string) (*AMSData, error) {
	// 发起HTTP请求
	resp, err := http.Get(amsURL)
	if err != nil {
		return nil, fmt.Errorf("请求AMS数据失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("AMS服务返回错误状态: %d", resp.StatusCode)
	}

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应内容失败: %v", err)
	}

	// 解析JSON
	var amsData AMSData
	if err := json.Unmarshal(body, &amsData); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %v", err)
	}

	return &amsData, nil
}

// 创建活动和流程
func (s *ActivityAdminService) CreateActivityAndFlows(ctx context.Context, amsURL string) (*string, error) {
	// 获取AMS数据
	amsData, err := s.FetchAMSData(amsURL)
	if err != nil {
		return nil, fmt.Errorf("获取AMS数据失败: %v", err)
	}

	// 解析活动信息
	parsedActivity, err := s.ParseActivity(amsData)
	if err != nil {
		return nil, fmt.Errorf("解析活动信息失败: %v", err)
	}

	// 获取活动ID
	activityID := parsedActivity.ActivityId

	// 检查活动是否已存在，如果存在则更新，否则创建
	var activity *model.Activity
	existingActivity, err := s.GetActivityById(ctx, activityID)
	if err == nil && existingActivity != nil {
		// 活动已存在，更新活动信息
		updateActivityReq := &model.Activity{
			ActivityName: parsedActivity.ActivityName,
			Status:       parsedActivity.Status,
			Priority:     parsedActivity.Priority,
			BeginTime:    parsedActivity.BeginTime,
			EndTime:      parsedActivity.EndTime,
		}

		activity, err = s.UpdateActivity(ctx, activityID, updateActivityReq)
		if err != nil {
			return nil, fmt.Errorf("更新活动失败: %v", err)
		}
	} else {
		// 活动不存在，创建新活动
		activity = &model.Activity{
			ActivityId:   activityID,
			ActivityName: parsedActivity.ActivityName,
			Status:       byte(parsedActivity.Status),
			Priority:     byte(parsedActivity.Priority),
			BeginTime:    parsedActivity.BeginTime,
			EndTime:      parsedActivity.EndTime,
		}

		activity, err = s.CreateActivity(ctx, activity)
		if err != nil {
			return nil, fmt.Errorf("创建活动失败: %v", err)
		}
	}

	// 创建流程
	flowCount := 0
	if len(parsedActivity.Flows) > 0 && s.activityService != nil {
		flowCount, _ = s.CreateFlowsFromAMS(ctx, activity.ActivityId, parsedActivity.Flows)
	}

	// 根据是否为更新操作生成不同的消息
	var message string
	if existingActivity != nil {
		message = fmt.Sprintf("成功更新活动信息，新增 %d 个流程", flowCount)
	} else {
		message = fmt.Sprintf("成功创建活动草稿，包含 %d 个流程", flowCount)
	}

	return &message, nil
}

// 从AMS数据批量创建流程
func (s *ActivityAdminService) CreateFlowsFromAMS(ctx context.Context, activityId uint64, flows []model.ActivityFlow) (int, error) {
	successCount := 0
	var existingFlows = []model.ActivityFlow{}

	// 获取现有流程列表
	activity, err := s.GetActivityById(ctx, activityId)
	if err == nil {
		existingFlows = activity.Flows
	}

	// 创建现有流程ID的映射，用于快速查找
	existingFlowIds := make(map[uint64]bool)
	for _, existingFlow := range existingFlows {
		existingFlowIds[existingFlow.FlowId] = true
	}

	for _, flow := range flows {
		// 检查流程是否已存在
		if existingFlowIds[flow.FlowId] {
			logger.Info("流程 %d 已存在，跳过创建", flow.FlowId)
			continue
		}

		// 设置活动ID
		flow.ActivityID = activityId

		if _, err := s.CreateFlow(ctx, &flow); err == nil {
			successCount++
			logger.Info("成功创建新流程: %d - %s", flow.FlowId, flow.FlowName)
		} else {
			logger.Error("创建流程 %d 失败: %v", flow.FlowId, err)
		}
	}

	return successCount, nil
}

// 解析活动信息
func (s *ActivityAdminService) ParseActivity(amsData *AMSData) (*model.Activity, error) {
	// 解析活动ID
	activityIdStr := amsData.ActivityID
	activityId, err := strconv.ParseUint(activityIdStr, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("无效的活动ID: %s", activityIdStr)
	}

	// 解析时间
	beginTime := time.Now().Add(24 * time.Hour)    // 默认明天
	endTime := time.Now().Add(30 * 24 * time.Hour) // 默认30天后
	if t, err := time.Parse("2006-01-02 15:04:05", amsData.BeginTimeString); err == nil {
		beginTime = t
	}

	if t, err := time.Parse("2006-01-02 15:04:05", amsData.EndTimeString); err == nil {
		endTime = t
	}

	return &model.Activity{
		ActivityId:   activityId,
		ActivityName: DecodeUnicodeString(amsData.ActivityName),
		Status:       0, // 草稿状态
		Priority:     5,
		BeginTime:    beginTime,
		EndTime:      endTime,
		Flows:        s.parseFlows(amsData),
	}, nil
}

// 解析流程信息
func (s *ActivityAdminService) parseFlows(amsData *AMSData) []model.ActivityFlow {
	var flows []model.ActivityFlow
	sortOrder := 1

	// 从ide.flows解析
	if amsData.IDE != nil && amsData.IDE.Flows != nil {
		for flowIDStr, flowData := range amsData.IDE.Flows {
			// 转换flowID为数字
			flowID, err := strconv.ParseUint(flowIDStr, 10, 64)
			if err != nil {
				logger.Warn("无效的流程ID: %s", flowIDStr)
				continue
			}

			// 使用Name字段作为流程名称
			flowName := DecodeUnicodeString(flowData.Name)

			// 构建参数对象
			parameters := make(map[string]interface{})
			if flowData.InputParams != nil {
				for _, param := range flowData.InputParams {
					if param.Key != "" {
						parameters[param.Key] = map[string]interface{}{
							"desc":  DecodeUnicodeString(param.Desc),
							"value": "",
						}
					}
				}
			}

			// 序列化参数为JSON字符串
			parametersJSON, _ := json.Marshal(parameters)

			// 根据TplType设置状态
			status := byte(1) // 默认启用
			tplType := flowData.TplType

			// bindarea#query_map_id# 流程默认不启用
			if tplType == "bindarea#query_map_id#" {
				status = 0
			}

			flow := model.ActivityFlow{
				FlowId:     flowID,
				FlowName:   flowName,
				IdeToken:   flowData.IdeToken,
				TplType:    tplType,
				Parameters: string(parametersJSON),
				Status:     status,
				SortOrder:  sortOrder,
			}

			// 为特殊流程设置临时标记，用于后续排序
			if tplType == "bindarea#query_map_id#" || tplType == "bindarea#bind_map_id#" {
				// 使用临时字段标记，在排序后会被移除
				flow.SortOrder = -1 // 临时标记，表示需要排到前面
			}

			flows = append(flows, flow)
			sortOrder++
		}
	}

	// 对流程进行排序：bindarea 相关流程排到前面
	sort.Slice(flows, func(i, j int) bool {
		// 如果 i 是特殊流程（SortOrder = -1），排到前面
		if flows[i].SortOrder == -1 && flows[j].SortOrder != -1 {
			return true
		}
		// 如果 j 是特殊流程，i 排到后面
		if flows[j].SortOrder == -1 && flows[i].SortOrder != -1 {
			return false
		}
		// 如果都是特殊流程，按原顺序
		if flows[i].SortOrder == -1 && flows[j].SortOrder == -1 {
			return i < j
		}
		// 普通流程按 SortOrder 排序
		return flows[i].SortOrder < flows[j].SortOrder
	})

	// 重新设置正确的 SortOrder
	for i := range flows {
		flows[i].SortOrder = i + 1
	}

	return flows
}

// 辅助方法：解码Unicode字符串
func DecodeUnicodeString(str string) string {
	re := regexp.MustCompile(`\\u([0-9a-fA-F]{4})`)
	return re.ReplaceAllStringFunc(str, func(match string) string {
		codeStr := match[2:] // 去掉 \u
		if code, err := strconv.ParseInt(codeStr, 16, 32); err == nil {
			return string(rune(code))
		}
		return match
	})
}
