package service

import (
	"strconv"
	"time"
	"udp-server/server/internal/database"
	"udp-server/server/internal/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// 用户管理后台服务
type UserAdminService struct {
	db    *gorm.DB
	cache cache.Cache
}

// 创建用户管理后台服务
func NewUserAdminService() *UserAdminService {
	return &UserAdminService{
		db: database.GetDB(),
	}
}

// 获取用户列表
func (s *UserAdminService) GetUsers(page, limit int, status, search string, authService *service.AuthService, permissionService *service.PermissionService) ([]model.User, error) {
	var users []model.User
	offset := (page - 1) * limit

	// 构建查询
	query := s.db.Model(&model.User{})

	// 搜索条件
	if search != "" {
		// 尝试将搜索词转换为数字（用于QQ号搜索）
		if qqNumber, err := strconv.ParseUint(search, 10, 64); err == nil {
			query = query.Where("uin = ? OR name LIKE ?", qqNumber, "%"+search+"%")
		} else {
			query = query.Where("name LIKE ?", "%"+search+"%")
		}
	}

	// 状态筛选
	if status, err := strconv.Atoi(status); err == nil {
		query = query.Where("status = ?", status)
	}

	// 如果有在线状态筛选或权限筛选，需要获取更多数据进行内存筛选
	queryLimit := limit
	queryOffset := offset
	err := query.Order("uid DESC").Offset(queryOffset).Limit(queryLimit).Find(&users).Error
	if err != nil {
		return nil, err
	}

	result := make([]map[string]interface{}, 0, len(users))
	for _, user := range users {
		// 检查用户是否在线
		isOnline := authService.IsUserOnline(int64(user.Uin))

		// 获取权限过期时间和权限等级
		var permissionExpiration int64
		var permissionExpirationText string
		var permissionLevel uint8 = 0

		if permissionService != nil {
			// 获取权限过期时间
			expiration, err := permissionService.GetPermissionExpiration(user.UID, "client")
			if err != nil {
				logger.Warn("获取用户权限过期时间失败: UID=%d, Error=%v", user.UID, err)
				permissionExpiration = 0
				permissionExpirationText = "获取失败"
			} else {
				permissionExpiration = expiration
				permissionExpirationText = s.formatPermissionExpiration(expiration)
			}

			// 获取基于CDKey的权限等级
			level, err := permissionService.GetUserPermissionLevel(user.UID, "client")
			if err != nil {
				logger.Warn("获取用户权限等级失败: UID=%d, Error=%v", user.UID, err)
				permissionLevel = 0
			} else {
				permissionLevel = level
			}
		} else {
			permissionExpiration = 0
			permissionExpirationText = "未知"
			permissionLevel = 0
		}

		userInfo := map[string]interface{}{
			"uid":                        user.UID,
			"uin":                        user.Uin,
			"name":                       user.Name,       // 添加用户名字段
			"status":                     user.Status,     // 添加用户状态字段
			"permission":                 permissionLevel, // 使用基于CDKey计算的权限等级
			"permission_expiration":      permissionExpiration,
			"permission_expiration_text": permissionExpirationText,
			"is_online":                  isOnline,
			"login_time":                 user.GetLoginTime().Format("2006-01-02 15:04:05"),
			"token_time":                 user.GetTokenTime().Format("2006-01-02 15:04:05"),
		}

		result = append(result, userInfo)
	}

	return users, nil
}

// 获取特定用户信息
func (s *UserAdminService) GetUser(uin uint64, authService *service.AuthService) (*model.User, error) {
	var user model.User
	err := s.db.Where("uin = ?", uin).First(&user).Error
	if err != nil {
		logger.Error("获取用户信息失败: %v", err)
		return nil, err
	}

	// 检查用户是否在线
	isOnline := authService.IsUserOnline(int64(user.Uin))

	result := map[string]interface{}{
		"uid":        user.UID,
		"uin":        user.Uin,
		"name":       user.Name,
		"status":     user.Status,
		"is_online":  isOnline,
		"login_time": time.Unix(user.LoginTime, 0).Format("2006-01-02 15:04:05"),
		"created_at": time.Unix(user.LoginTime, 0).Format("2006-01-02 15:04:05"),
	}

	if user.LoginTime > 0 {
		result["login_time"] = time.Unix(user.LoginTime, 0).Format("2006-01-02 15:04:05")
	} else {
		result["login_time"] = "从未登录"
	}

	return &user, nil
}

// 踢出用户
func (s *UserAdminService) KickUser(uin uint64, reason string) error {
	// TODO: 实现踢出用户逻辑
	logger.Info("踢出用户: UID=%d, reason=%s", uin, reason)
	return nil
}

// 获取用户登录历史
func (s *UserAdminService) GetUserLoginHistory(uin uint64, page, limit int) ([]map[string]interface{}, int64, error) {
	// TODO: 实现获取用户登录历史
	logger.Info("获取用户登录历史: UID=%d, page=%d, limit=%d", uin, page, limit)
	return []map[string]interface{}{}, 0, nil
}

// 更新用户状态
func (s *UserAdminService) UpdateUserStatus(uin uint64, status int) error {
	err := s.db.Model(&model.User{}).Where("uin = ?", uin).Update("status", status).Error
	if err != nil {
		logger.Error("更新用户状态失败: UIN=%d, Status=%d, Error=%v", uin, status, err)
		return err
	}

	logger.Info("用户状态更新成功: UIN=%d, Status=%d", uin, status)
	return nil
}

// 格式化权限过期时间
func (s *UserAdminService) formatPermissionExpiration(expiration int64) string {
	switch expiration {
	case -1:
		return "永久"
	case 0:
		return "无权限"
	default:
		return time.Unix(expiration, 0).Format("2006-01-02 15:04:05")
	}
}
