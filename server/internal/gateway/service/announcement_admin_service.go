package service

import (
	"fmt"
	"time"

	"udp-server/server/internal/database"
	"udp-server/server/internal/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// 管理后台公告服务
type AnnouncementAdminService struct {
	db                  *gorm.DB
	announcementService *service.AnnouncementService
}

// 创建服务实例
func NewAnnouncementAdminService(announcementService *service.AnnouncementService) *AnnouncementAdminService {
	return &AnnouncementAdminService{
		db:                  database.GetDB(),
		announcementService: announcementService,
	}
}

// 获取公告总数
func (s *AnnouncementAdminService) GetAnnouncementCount() int64 {
	var count int64
	s.db.Model(&model.Announcement{}).Count(&count)
	return count
}

// 获取所有公告
func (s *AnnouncementAdminService) GetAllAnnouncements() ([]model.Announcement, error) {
	var announcements []model.Announcement
	err := s.db.Order("id DESC").Find(&announcements).Error
	if err != nil {
		logger.Error("查询所有公告失败: %v", err)
		return nil, err
	}
	return announcements, nil
}

// 根据ID获取公告
func (s *AnnouncementAdminService) GetAnnouncementByID(id uint64) (*model.Announcement, error) {
	var announcement model.Announcement
	err := s.db.Where("id = ?", id).First(&announcement).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("公告不存在")
		}
		logger.Error("获取公告详情失败: %v", err)
		return nil, err
	}
	return &announcement, nil
}

// 创建公告
func (s *AnnouncementAdminService) CreateAnnouncement(title, content string, Type uint8, priority uint8, status uint8, adminUID uint32) (uint32, error) {
	announcement := &model.Announcement{
		Title:     title,
		Content:   content,
		Type:      Type,
		Priority:  priority,
		Status:    status,
		Version:   1,
		AdminUID:  adminUID,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err := s.db.Create(announcement).Error
	if err != nil {
		logger.Error("创建公告失败: %v", err)
		return 0, err
	}

	// 如果是发布状态，清理缓存并更新版本号
	if status == 1 && s.announcementService != nil {
		s.announcementService.UpdateAnnouncementCache(announcement.ID)
		s.announcementService.IncrementVersion()
	}

	logger.Info("公告创建成功: ID=%d, 标题=%s, 状态=%d", announcement.ID, title, status)
	return announcement.ID, nil
}

// 更新公告
func (s *AnnouncementAdminService) UpdateAnnouncement(id uint32, title, content string, Type uint8, priority uint8, status uint8, adminUID uint32) error {
	// 先检查公告是否存在
	var announcement model.Announcement
	err := s.db.Where("id = ?", id).First(&announcement).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("公告不存在")
		}
		logger.Error("查询公告失败: %v", err)
		return err
	}

	// 更新公告（为更新的公告生成新版本号）
	updateData := map[string]interface{}{
		"title":      title,
		"content":    content,
		"type":       Type,
		"priority":   priority,
		"status":     status,
		"version":    announcement.Version + 1,
		"admin":      adminUID,
		"updated_at": time.Now(),
	}

	err = s.db.Model(&model.Announcement{}).Where("id = ?", id).Updates(updateData).Error
	if err != nil {
		logger.Error("更新公告失败: %v", err)
		return err
	}

	// 清理缓存并更新版本号（任何更新都可能影响显示）
	if s.announcementService != nil {
		s.announcementService.UpdateAnnouncementCache(id)
		s.announcementService.IncrementVersion()
	}

	logger.Info("公告更新成功: ID=%d, 标题=%s, 状态=%d", id, title, status)
	return nil
}

// 删除公告
func (s *AnnouncementAdminService) DeleteAnnouncement(id uint32) error {
	// 先检查公告是否存在
	var announcement model.Announcement
	err := s.db.Where("id = ?", id).First(&announcement).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("公告不存在")
		}
		logger.Error("查询公告失败: %v", err)
		return err
	}

	// 删除公告
	err = s.db.Delete(&model.Announcement{}, id).Error
	if err != nil {
		logger.Error("删除公告失败: %v", err)
		return err
	}

	// 清理缓存并更新版本号
	if s.announcementService != nil {
		s.announcementService.UpdateAnnouncementCache(id)
		// 增加版本号
		s.announcementService.IncrementVersion()
	}

	logger.Info("公告删除成功: ID=%d", id)
	return nil
}

// 获取公告统计信息
func (s *AnnouncementAdminService) GetAnnouncementStats() (map[string]int, error) {
	stats := make(map[string]int)

	// 统计各状态的公告数量
	var counts []struct {
		Status int8 `json:"status"`
		Count  int  `json:"count"`
	}

	err := s.db.Model(&model.Announcement{}).
		Select("status, count(*) as count").
		Group("status").
		Find(&counts).Error

	if err != nil {
		logger.Error("获取公告统计失败: %v", err)
		return nil, err
	}

	// 初始化统计数据
	stats["total"] = 0
	stats["draft"] = 0     // 草稿
	stats["published"] = 0 // 已发布
	stats["offline"] = 0   // 已下线

	// 填充统计数据
	for _, count := range counts {
		stats["total"] += count.Count
		switch count.Status {
		case 0:
			stats["draft"] = count.Count
		case 1:
			stats["published"] = count.Count
		case 2:
			stats["offline"] = count.Count
		}
	}

	return stats, nil
}
