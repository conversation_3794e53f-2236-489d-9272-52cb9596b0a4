package service

import (
	"fmt"
	"strconv"
	"time"

	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// 更新配置管理后台服务
type UpdateAdminService struct {
	db    *gorm.DB
	cache cache.Cache
}

// 创建更新配置管理后台服务
func NewUpdateAdminService(db *gorm.DB, cache cache.Cache) *UpdateAdminService {
	return &UpdateAdminService{
		db:    db,
		cache: cache,
	}
}

// 获取所有更新配置
func (s *UpdateAdminService) GetConfigs() ([]model.UpdateConfig, error) {
	var configs []model.UpdateConfig

	err := s.db.Order("id ASC").Find(&configs).Error
	if err != nil {
		return nil, fmt.Errorf("获取更新配置失败: %v", err)
	}

	return configs, nil
}

// 获取特定更新配置详情
func (s *UpdateAdminService) GetConfig(id uint64) (*model.UpdateConfig, error) {
	var config model.UpdateConfig
	err := s.db.Where("id = ?", id).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("获取配置详情失败: %v", err)
	}

	return &config, nil
}

// 创建更新配置
func (s *UpdateAdminService) CreateConfig(req model.UpdateConfig) (uint64, error) {
	err := s.db.Create(req).Error
	if err != nil {
		return 0, fmt.Errorf("创建配置失败: %v", err)
	}

	// 清除相关缓存
	s.clearUpdateCache(req.Name)
	return req.ID, nil
}

// 更新配置
func (s *UpdateAdminService) UpdateConfig(req model.UpdateConfig) error {
	// 这个模型是临时的，需要构建更新字典
	updates := map[string]interface{}{
		"name":       req.Name,
		"version":    req.Version,
		"url":        req.URL,
		"checksum":   req.Checksum,
		"is_active":  req.IsActive,
		"force":      req.Force,
		"updated_at": time.Now(),
	}

	err := s.db.Model(&model.UpdateConfig{}).Where("id = ?", req.ID).Updates(updates).Error
	if err != nil {
		return fmt.Errorf("更新配置失败: %v", err)
	}

	// 清除相关缓存
	s.clearUpdateCache(req.Name)
	return nil
}

// 删除配置
func (s *UpdateAdminService) DeleteConfig(id uint64) error {
	// 先获取配置名称用于清除缓存
	var config model.UpdateConfig
	err := s.db.Select("name").Where("id = ?", id).First(&config).Error
	if err != nil {
		return fmt.Errorf("配置不存在")
	}

	err = s.db.Delete(&model.UpdateConfig{}, id).Error
	if err != nil {
		return fmt.Errorf("删除配置失败: %v", err)
	}

	// 清除相关缓存
	s.clearUpdateCache(config.Name)
	return nil
}

// 清除更新配置缓存
func (s *UpdateAdminService) clearUpdateCache(appName string) {
	if s.cache != nil {
		s.cache.Delete(fmt.Sprintf("update_config:%s:prod", appName))
		s.cache.Delete(fmt.Sprintf("update_config:%s:test", appName))
		logger.Info("更新配置缓存已清除: %s", appName)
	}
}

// 获取白名单群组配置
func (s *UpdateAdminService) GetGroups() ([]model.WhitelistGroup, error) {
	var groups []model.WhitelistGroup

	err := s.db.Order("id ASC").Find(&groups).Error
	if err != nil {
		return nil, fmt.Errorf("获取群组配置失败: %v", err)
	}

	return groups, nil
}

// 添加白名单群组配置
func (s *UpdateAdminService) AddGroup(groupIDStr, description string) error {
	// 将字符串转换为int64
	groupID, err := strconv.ParseInt(groupIDStr, 10, 64)
	if err != nil {
		return fmt.Errorf("无效的群组ID格式: %s", groupIDStr)
	}

	// 检查群组是否已存在
	var existingGroup model.WhitelistGroup
	err = s.db.Where("group_id = ?", groupID).First(&existingGroup).Error
	if err == nil {
		return fmt.Errorf("群组ID %d 已存在", groupID)
	} else if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("检查群组失败: %v", err)
	}

	// 创建新群组
	group := &model.WhitelistGroup{
		GroupID:     groupID,
		Description: description,
		IsActive:    true,
	}

	err = s.db.Create(group).Error
	if err != nil {
		return fmt.Errorf("添加群组失败: %v", err)
	}

	// 刷新缓存
	s.RefreshGroupsCache()
	return nil
}

// 切换白名单群组状态
func (s *UpdateAdminService) ToggleGroup(id uint64) error {
	var group model.WhitelistGroup
	err := s.db.First(&group, id).Error
	if err != nil {
		return fmt.Errorf("群组不存在")
	}

	// 切换状态
	group.IsActive = !group.IsActive
	err = s.db.Save(&group).Error
	if err != nil {
		return fmt.Errorf("更新群组状态失败: %v", err)
	}

	// 刷新缓存
	s.RefreshGroupsCache()

	return nil
}

// 删除白名单群组
func (s *UpdateAdminService) DeleteGroup(id uint64) error {
	err := s.db.Delete(&model.WhitelistGroup{}, id).Error
	if err != nil {
		return fmt.Errorf("删除群组失败: %v", err)
	}

	// 刷新缓存
	s.RefreshGroupsCache()
	return nil
}

// 刷新群组缓存
func (s *UpdateAdminService) RefreshGroupsCache() {
	if s.cache != nil {
		// 刷新白名单群组缓存
		whitelistKey := "whitelist_groups"
		s.cache.Delete(whitelistKey)

		// 刷新活跃群组缓存
		activeKey := "active_groups"
		s.cache.Delete(activeKey)
		logger.Info("群组缓存已刷新")
	}
}
