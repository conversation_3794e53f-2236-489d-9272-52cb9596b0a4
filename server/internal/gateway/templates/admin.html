{{define "title"}}{{.Title}}{{end}}

{{define "head"}}
<link rel="stylesheet" href="/static/css/admin.css?v=20250711-3">
{{end}}

{{define "navigation"}}
<a href="#dashboard" class="nav-item active">仪表板</a>
<a href="#announcements" class="nav-item">公告管理</a>
<a href="#activity" class="nav-item">活动管理</a>
<a href="#users" class="nav-item">用户管理</a>
<a href="#cdkeys" class="nav-item">口令码管理</a>
<a href="#admins" class="nav-item">管理员管理</a>
<a href="#risk" class="nav-item">风控管理</a>
<a href="#updates" class="nav-item">更新管理</a>
<a href="javascript:void(0)" class="nav-item logout" onclick="logout(); return false;">退出</a>
{{end}}

{{define "main_content"}}
        <!-- 仪表板 -->
        <div id="dashboard" class="section active">
            <div class="dashboard">
                <div class="card">
                    <h3>总用户数</h3>
                    <div class="stat-value" id="total-users">-</div>
                    <div class="stat-label">注册用户</div>
                </div>
                <div class="card">
                    <h3>登录用户</h3>
                    <div class="stat-value" id="auth-users">-</div>
                    <div class="stat-label">登录用户</div>
                </div>
                <div class="card">
                    <h3>实时用户</h3>
                    <div class="stat-value" id="active-users">-</div>
                    <div class="stat-label">实时用户</div>
                </div>
                <div class="card">
                    <h3>审核用户</h3>
                    <div class="stat-value" id="review-users">-</div>
                    <div class="stat-label">待审核</div>
                </div>
                <div class="card">
                    <h3>服务器状态</h3>
                    <div class="stat-value" style="color: #38a169;">运行中</div>
                    <div class="stat-label" id="server-uptime">-</div>
                </div>
            </div>

            <!-- 在线用户统计图表 -->
            <div style="margin-top: 30px;">
                <div class="content">
                    <div class="section-header">
                        <h2>在线用户统计</h2>
                        <div class="section-actions">
                            <select id="chart-period" style="padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px; margin-right: 10px;" onchange="loadOnlineStatsChart()">
                                <option value="hour">最近24小时</option>
                                <option value="day">最近7天</option>
                                <option value="day15">最近15天</option>
                                <option value="day30">最近30天</option>
                            </select>
                            <button class="btn btn-success" onclick="loadOnlineStatsChart()">刷新</button>
                        </div>
                    </div>
                    <div id="online-stats-chart" style="height: 400px; width: 100%; background: white; border-radius: 8px; padding: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                        <canvas id="stats-chart-canvas" style="width: 100%; height: 100%;"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 公告管理 -->
        <div id="announcements" class="section">
            <div class="content">
                <div class="section-header">
                    <h2>公告管理</h2>
                    <div class="section-actions">
                        <button class="btn" onclick="showCreateAnnouncementModal()">新建公告</button>
                    </div>
                </div>
                <div style="margin-bottom: 15px; padding: 15px; background: #edf2f7; border-radius: 6px; border-left: 4px solid #4a5568;">
                    <h4 style="margin: 0 0 8px 0; color: #2d3748;">公告管理说明</h4>
                    <p style="margin: 0; color: #4a5568; font-size: 14px;">
                        创建和管理系统公告，支持设置优先级、发布时间、结束时间等。公告将按优先级和时间顺序显示给用户。
                    </p>
                </div>
                <div id="announcements-content" class="loading">
                    <div style="overflow-x: auto;">
                        <table id="announcements-table" style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                            <thead style="background: #f7fafc;">
                                <tr>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">ID</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">标题</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">状态</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">创建时间</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="announcements-table-body">
                                <tr>
                                    <td colspan="5" style="padding: 40px; text-align: center; color: #718096;">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 用户管理 -->
        <div id="users" class="section">
            <div class="content">
                <div class="section-header">
                    <h2>用户管理</h2>
                    <div class="section-actions">
                        <select id="user-status-filter" style="padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px; margin-right: 10px;" onchange="filterUsers()">
                            <option value="">全部状态</option>
                            <option value="0">正常</option>
                            <option value="1">临时封禁</option>
                            <option value="2">永久封禁</option>
                            <option value="3">审核中</option>
                        </select>
                        <select id="user-online-filter" style="padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px; margin-right: 10px;" onchange="filterUsers()">
                            <option value="">全部用户</option>
                            <option value="online">在线用户</option>
                            <option value="offline">离线用户</option>
                        </select>
                        <select id="user-permission-filter" style="padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px; margin-right: 10px;" onchange="filterUsers()">
                            <option value="">全部权限</option>
                            <option value="0">普通用户</option>
                            <option value="1">高级用户</option>
                            <option value="2">超级用户</option>
                            <!-- <option value="3">特级用户</option> -->
                        </select>
                        <input type="text" id="user-search-input" placeholder="搜索用户QQ号..." style="padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px; width: 200px;">
                        <button class="btn" onclick="searchUsers()">搜索</button>
                        <button class="btn btn-success" onclick="loadUsersData()">刷新</button>
                    </div>
                </div>
                <div style="margin-bottom: 15px; padding: 15px; background: #edf2f7; border-radius: 6px; border-left: 4px solid #4a5568;">
                    <h4 style="margin: 0 0 8px 0; color: #2d3748;">用户管理说明</h4>
                    <p style="margin: 0; color: #4a5568; font-size: 14px;">
                        查看和管理系统用户，包括用户状态、权限级别、在线状态等。可以踢出在线用户或查看用户详细信息。
                    </p>
                </div>
                <div id="users-content" class="loading">
                    <div style="overflow-x: auto;">
                        <table id="users-table" style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                            <thead style="background: #f7fafc;">
                                <tr>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">用户ID</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">QQ号</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">用户名</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">状态</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">权限</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">权限过期</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">在线状态</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">最后登录</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="users-table-body">
                                <tr>
                                    <td colspan="9" style="padding: 40px; text-align: center; color: #718096;">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 口令码管理 -->
        <div id="cdkeys" class="section">
            <div class="content">
                <div class="section-header">
                    <h2>口令码管理</h2>
                    <div class="section-actions">
                        <select id="cdkey-type-filter" style="padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px; margin-right: 10px;" onchange="searchCDKeys()">
                            <option value="">全部类型</option>
                            <option value="战斗记录">战斗记录</option>
                            <option value="自定义查询">自定义查询</option>
                            <option value="抽奖次数">抽奖次数</option>
                        </select>
                        <select id="cdkey-status-filter" style="padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px; margin-right: 10px;" onchange="searchCDKeys()">
                            <option value="">全部状态</option>
                            <option value="active">可用</option>
                            <option value="used">已使用</option>
                            <option value="expired">已过期</option>
                            <option value="unused">未使用</option>
                        </select>
                        <input type="text" id="cdkey-search-input" placeholder="搜索口令码..." style="padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px; width: 200px;" onkeypress="if(event.key==='Enter') searchCDKeys()">
                        <button class="btn" onclick="searchCDKeys()">搜索</button>
                        <button class="btn" onclick="showCreateCDKeyModal()">生成口令码</button>
                        <button class="btn btn-success" onclick="loadCDKeysData()">刷新</button>
                    </div>
                </div>

                <div style="margin-bottom: 15px; padding: 15px; background: #edf2f7; border-radius: 6px; border-left: 4px solid #38a169;">
                    <h4 style="margin: 0 0 8px 0; color: #2d3748;">口令码管理说明</h4>
                    <p style="margin: 0; color: #4a5568; font-size: 14px;">
                        生成和管理系统口令码，支持战斗记录时长、抽奖次数、自定义查询等类型。点击口令码可查看使用记录。
                    </p>
                </div>

                <div id="cdkeys-content" class="loading">
                    <div style="overflow-x: auto;">
                        <table id="cdkeys-table" style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                            <thead style="background: #f7fafc;">
                                <tr>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">口令码</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">类型</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">状态</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">开始时间</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">结束时间</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">可用期限</th>
                                </tr>
                            </thead>
                            <tbody id="cdkeys-table-body">
                                <tr>
                                    <td colspan="6" style="padding: 40px; text-align: center; color: #718096;">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 管理员管理 -->
        <div id="admins" class="section">
            <div class="content">
                <div class="section-header">
                    <h2>管理员管理</h2>
                    <div class="section-actions">
                        <button class="btn" onclick="showCreateAdminModal()" id="add-admin-btn" style="display: none;">添加管理员</button>
                    </div>
                </div>
                <div style="margin-bottom: 15px; padding: 15px; background: #f0f8ff; border-radius: 6px; border-left: 4px solid #2b6cb0;">
                    <h4 style="margin: 0 0 8px 0; color: #2d3748;">管理员管理说明</h4>
                    <p style="margin: 0; color: #4a5568; font-size: 14px;">
                        管理系统管理员账号，包括创建、编辑和删除管理员。只有超级管理员才能进行管理员相关操作。
                    </p>
                </div>
                <div id="admins-content" class="loading">
                    <div style="overflow-x: auto;">
                        <table id="admins-table" style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                            <thead style="background: #f7fafc;">
                                <tr>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">ID</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">用户名</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">权限</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">类型</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">状态</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="admins-table-body">
                                <tr>
                                    <td colspan="6" style="padding: 40px; text-align: center; color: #718096;">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 风控管理 -->
        <div id="risk" class="section">
            <div class="content">
                <div class="section-header">
                    <h2>风控管理</h2>
                    <div class="section-actions">
                        <button class="btn" onclick="showRiskConfigModal()">风控配置</button>
                        <button class="btn btn-secondary" onclick="showIgnoredEventsModal()">忽略管理</button>
                        <button class="btn btn-success" onclick="loadRiskData()">刷新</button>
                    </div>
                </div>
                <div style="margin-bottom: 15px; padding: 15px; background: #fef5e7; border-radius: 6px; border-left: 4px solid #d69e2e;">
                    <h4 style="margin: 0 0 8px 0; color: #2d3748;">风控系统说明</h4>
                    <p style="margin: 0; color: #4a5568; font-size: 14px;">
                        监控和管理用户登录行为，防止异常登录和恶意行为。包括设备限制、IP限制、频率限制等多种风控策略。
                    </p>
                </div>

                <div id="risk-content" class="loading">
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                            <thead style="background: #f7fafc;">
                                <tr>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">编号</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">用户QQ</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">描述</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">等级</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">时间</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="risk-table-body">
                                <tr>
                                    <td colspan="7" style="padding: 40px; text-align: center; color: #718096;">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div id="risk-pagination" style="text-align: center; margin-top: 20px;">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 更新管理 -->
        <div id="updates" class="section">
            <div class="content">
                <div class="section-header">
                    <h2>更新管理</h2>
                    <div class="section-actions">
                        <button class="btn" onclick="showUpdateConfigModal()">添加更新配置</button>
                        <button class="btn btn-success" onclick="loadUpdatesData()">刷新</button>
                    </div>
                </div>
                <div style="margin-bottom: 15px; padding: 15px; background: #e6fffa; border-radius: 6px; border-left: 4px solid #38b2ac;">
                    <h4 style="margin: 0 0 8px 0; color: #2d3748;">更新管理说明</h4>
                    <p style="margin: 0; color: #4a5568; font-size: 14px;">
                        管理客户端更新配置，包括版本信息、下载链接、群组权限等。可以为不同的应用配置不同的更新策略和群组访问权限。
                    </p>
                </div>
                <div id="updates-content" class="loading">
                    <div style="overflow-x: auto;">
                        <table id="updates-table" style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                            <thead style="background: #f7fafc;">
                                <tr>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">配置ID</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">配置名称</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">当前版本</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">下载链接</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">状态</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">创建时间</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="updates-table-body">
                                <tr>
                                    <td colspan="7" style="padding: 40px; text-align: center; color: #718096;">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 活动管理 -->
        <div id="activity" class="section">
            <div class="content">
                <div class="section-header">
                    <h2>活动管理</h2>
                    <div class="section-actions">
                        <select id="activity-status-filter" style="padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px; margin-right: 10px;" onchange="loadActivitiesData()">
                            <option value="">全部状态</option>
                            <option value="0">草稿</option>
                            <option value="1">活跃</option>
                            <option value="2">已结束</option>
                        </select>
                        <input type="text" id="activity-search-input" placeholder="搜索活动..." style="padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px; width: 200px;" onkeypress="if(event.key==='Enter') loadActivitiesData()">
                        <button class="btn" onclick="loadActivitiesData()">搜索</button>
                        <button class="btn" onclick="showCreateActivityModal()">新建活动</button>
                        <button class="btn btn-primary" onclick="showImportAmsModal()">导入AMS链接</button>
                        <button class="btn btn-success" onclick="loadActivitiesData()">刷新</button>
                    </div>
                </div>
                <div style="margin-bottom: 15px; padding: 15px; background: #f0fff4; border-radius: 6px; border-left: 4px solid #38a169;">
                    <h4 style="margin: 0 0 8px 0; color: #2d3748;">活动管理说明</h4>
                    <p style="margin: 0; color: #4a5568; font-size: 14px;">
                        创建和管理系统活动，包括活动信息、流程配置、参数设置等。活动支持多种状态和优先级设置。
                    </p>
                </div>
                <div id="activities-content" class="loading">
                    <div style="overflow-x: auto;">
                        <table id="activities-table" style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                            <thead style="background: #f7fafc;">
                                <tr>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">活动ID</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">活动名称</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">状态</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">优先级</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">开始时间</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">结束时间</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="activities-table-body">
                                <tr>
                                    <td colspan="7" style="padding: 40px; text-align: center; color: #718096;">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div id="activities-pagination" style="text-align: center; margin-top: 20px;">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框容器 -->
    <div id="modal-container">
        <!-- 用户详情模态框 -->
        <div id="user-details-modal" class="modal" style="display: none;">
            <div class="modal-content" style="width: 700px; max-width: 90vw;">
                <button onclick="closeModal('user-details-modal')" class="modal-close-btn">×</button>
                <div class="modal-header">
                    <h3 id="user-details-title">用户详情</h3>
                </div>
                <div class="modal-body">
                    <div id="user-details-content">
                    </div>
                    <div class="form-actions" style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                        <button class="btn btn-primary" id="bind-cdkey-btn">绑定口令码</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑公告模态框 -->
        <div id="edit-announcement-modal" class="modal" style="display: none;">
            <div class="modal-content" style="width: 600px; max-width: 90vw;">
                <button onclick="closeModal('edit-announcement-modal')" class="modal-close-btn">×</button>
                <div class="modal-header">
                    <h3>编辑公告</h3>
                </div>
                <div class="modal-body">
                    <form id="edit-announcement-form">
                        <input type="hidden" id="edit-announcement-id" name="id">
                        <div class="form-group">
                            <label for="edit-announcement-title">公告标题</label>
                            <input type="text" id="edit-announcement-title" name="title" required style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label for="edit-announcement-content">公告内容</label>
                            <textarea id="edit-announcement-content" name="content" required style="width: 100%; height: 120px; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px; resize: vertical;"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="edit-announcement-type">公告类型</label>
                            <select id="edit-announcement-type" name="type" style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                                <option value="0">普通公告</option>
                                <option value="1">重要公告</option>
                                <option value="2">紧急公告</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-announcement-priority">优先级</label>
                            <input type="number" id="edit-announcement-priority" name="priority" min="1" max="10" value="5" style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label for="edit-announcement-status">状态</label>
                            <select id="edit-announcement-status" name="status" style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                                <option value="0">草稿</option>
                                <option value="1">已发布</option>
                                <option value="2">已下线</option>
                            </select>
                        </div>
                        <div class="form-actions" style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                            <button type="button" onclick="closeModal('edit-announcement-modal')" class="btn" style="background: #6b7280;">取消</button>
                            <button type="submit" class="btn">保存修改</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 编辑更新配置模态框 -->
        <div id="edit-update-modal" class="modal" style="display: none;">
            <div class="modal-content" style="width: 600px; max-width: 90vw;">
                <button onclick="closeModal('edit-update-modal')" class="modal-close-btn">×</button>
                <div class="modal-header">
                    <h3>编辑更新配置</h3>
                </div>
                <div class="modal-body">
                    <form id="edit-update-form">
                        <input type="hidden" name="id" id="edit-update-id">
                        <div class="form-group">
                            <label for="edit-update-name">配置名称</label>
                            <input type="text" name="name" id="edit-update-name" required style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label for="edit-update-version">版本号</label>
                            <input type="text" name="version" id="edit-update-version" required style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label for="edit-update-url">下载链接</label>
                            <input type="url" name="url" id="edit-update-url" required style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label for="edit-update-checksum">校验和</label>
                            <input type="text" name="checksum" id="edit-update-checksum" style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label style="display: flex; align-items: center; margin-bottom: 0;">
                                <input type="checkbox" name="is_active" id="edit-update-active" style="margin-right: 8px;">
                                启用此配置
                            </label>
                        </div>
                        <div class="form-group">
                            <label style="display: flex; align-items: center; margin-bottom: 0;">
                                <input type="checkbox" name="force" id="edit-update-force" style="margin-right: 8px;">
                                强制更新
                            </label>
                        </div>
                        <div class="form-actions" style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                            <button type="button" onclick="closeModal('edit-update-modal')" class="btn" style="background: #6b7280;">取消</button>
                            <button type="submit" class="btn">保存修改</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 创建更新配置模态框 -->
        <div id="create-update-modal" class="modal" style="display: none;">
            <div class="modal-content" style="width: 600px; max-width: 90vw;">
                <button onclick="closeModal('create-update-modal')" class="modal-close-btn">×</button>
                <div class="modal-header">
                    <h3>添加更新配置</h3>
                </div>
                <div class="modal-body">
                    <form id="create-update-form">
                        <div class="form-group">
                            <label for="create-update-name">应用名称</label>
                            <input type="text" name="name" id="create-update-name" required placeholder="例如: BnsHelper" style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label for="create-update-version">版本号</label>
                            <input type="text" name="version" id="create-update-version" required placeholder="例如: 1.0.0" style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label for="create-update-url">下载链接</label>
                            <input type="url" name="download_url" id="create-update-url" required placeholder="https://example.com/app.exe" style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label for="create-update-checksum">校验和（可选）</label>
                            <input type="text" name="checksum" id="create-update-checksum" placeholder="SHA256校验和" style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                        </div>
                        <div class="form-group">
                            <label style="display: flex; align-items: center; margin-bottom: 0;">
                                <input type="checkbox" name="is_active" id="create-update-active" style="margin-right: 8px;">
                                启用此配置
                            </label>
                        </div>
                        <div class="form-group">
                            <label style="display: flex; align-items: center; margin-bottom: 0;">
                                <input type="checkbox" name="force" id="create-update-force" style="margin-right: 8px;">
                                强制更新
                            </label>
                        </div>
                        <div class="form-actions" style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                            <button type="button" onclick="closeModal('create-update-modal')" class="btn" style="background: #6b7280;">取消</button>
                            <button type="submit" class="btn">添加</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 口令码使用记录模态框 -->
        <div id="cdkey-usage-records-modal" class="modal" style="display: none;">
            <div class="modal-content" style="width: 800px; max-width: 95vw;">
                <button onclick="closeModal('cdkey-usage-records-modal')" class="modal-close-btn">×</button>
                <div class="modal-header">
                    <h3>口令码使用记录</h3>
                </div>
                <div class="modal-body">
                    <div id="cdkey-usage-records-content" class="loading">
                        <div style="text-align: center; padding: 40px; color: #718096;">
                            加载中...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 生成口令码模态框 -->
        <div id="cdkey-modal" class="modal" style="display: none;">
            <div class="modal-content" style="width: 600px; max-width: 90vw;">
                <div class="modal-header">
                    <h3>生成口令码</h3>
                    <button type="button" class="modal-close" onclick="closeModal('cdkey-modal')">&times;</button>
                </div>
                <form id="cdkey-form" style="padding: 20px;">
                    <div class="form-grid">
                        <div class="form-row">
                            <div class="form-group">
                                <label>口令码类型</label>
                                <select name="type" id="cdkey-type-select" onchange="updatePrefixSuggestions()">
                                    <option value="client">战斗记录时长</option>
                                    <option value="drawtimes">额外抽奖次数</option>
                                    <option value="customize">装备查询自定义</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>生成数量</label>
                                <input type="number" name="count" value="10" min="1" max="1000" required onchange="toggleUsageCountField()">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>时间类型</label>
                                <select name="time_type" id="time-type-select" onchange="toggleTimeFields()">
                                    <option value="duration">激活后开始计算天数</option>
                                    <option value="fixed">固定日期结束</option>
                                </select>
                            </div>
                            <div class="form-group" id="duration-fields">
                                <label>激活天数</label>
                                <select name="duration">
                                    <option value="7">7天</option>
                                    <option value="15">15天</option>
                                    <option value="30" selected>30天</option>
                                    <option value="60">60天</option>
                                    <option value="90">90天</option>
                                    <option value="180">180天</option>
                                    <option value="365">365天</option>
                                </select>
                            </div>
                            <div class="form-group" id="fixed-time-fields" style="display: none;">
                                <label>过期时间</label>
                                <input type="date" name="fixed_time">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>激活开放时间</label>
                                <input type="date" name="start_time">
                            </div>
                            <div class="form-group">
                                <label>激活结束时间</label>
                                <input type="date" name="end_time">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>前缀（可选）</label>
                                <input type="text" name="prefix" id="prefix-input" placeholder="例如: BNS">
                                <div class="prefix-suggestions">
                                    <span>建议：</span>
                                    <a href="javascript:void(0)" onclick="setPrefix('GIFT')">GIFT</a>
                                    <a href="javascript:void(0)" onclick="setPrefix('EVENT')">EVENT</a>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>描述</label>
                                <input type="text" name="description" placeholder="口令码用途描述">
                            </div>
                        </div>

                        <div class="form-row" id="usage-count-field" style="display: none;">
                            <div class="form-group">
                                <label>可使用数量</label>
                                <input type="number" name="usage_count" value="1" min="1" max="100">
                                <small>仅在生成数量为1时可设置</small>
                            </div>
                        </div>
                    </div>

                    <!-- 分割线 -->
                    <div class="form-divider"></div>

                    <!-- 安全验证与确认生成 -->
                    <div class="security-verification">
                        <div class="captcha-submit-row">
                            <div id="captcha-display" class="captcha-display" onclick="refreshCaptcha()" title="点击刷新验证码"></div>
                            <input type="text" id="captcha-input" class="captcha-input" placeholder="请输入验证码" required>
                            <button type="submit" class="submit-btn-inline">
                                <span class="btn-icon">🔒</span>
                                <span class="btn-text">确认生成</span>
                            </button>
                        </div>
                        <input type="hidden" id="captcha-answer" value="">
                    </div>
                </form>
            </div>
        </div>

        <!-- 创建活动模态框 -->
        <div id="create-activity-modal" class="modal" style="display: none;">
            <div class="modal-content" style="width: 800px; max-width: 90vw;">
                <button onclick="closeModal('create-activity-modal')" class="modal-close-btn">×</button>
                <div class="modal-header">
                    <h3>新建活动</h3>
                </div>
                <div class="modal-body">
                    <form id="create-activity-form">
                        <div class="form-group">
                            <label for="create-activity-id">活动ID</label>
                            <input type="number" name="activity_id" id="create-activity-id" required placeholder="例如: 1001">
                        </div>
                        <div class="form-group">
                            <label for="create-activity-name">活动名称</label>
                            <input type="text" name="activity_name" id="create-activity-name" required placeholder="例如: 夏日狂欢活动">
                        </div>
                        <div class="form-group">
                            <label for="create-activity-url">活动链接</label>
                            <div style="display: flex; gap: 8px; align-items: center;">
                                <input type="text" name="activity_url" id="create-activity-url">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="create-activity-priority">优先级 (0-10)</label>
                            <input type="number" name="priority" id="create-activity-priority" min="0" max="10" value="5" required>
                        </div>
                        <div class="form-group">
                            <label for="create-activity-service-type">服务类型</label>
                            <select name="service_type" id="create-activity-service-type" style="width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px; background: #ffffff;">
                                <option value="0">正式服</option>
                                <option value="1" selected="">怀旧服</option>
                                <option value="2">巅峰服</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="create-activity-begin-time">开始时间</label>
                            <input type="datetime-local" name="begin_time" id="create-activity-begin-time" required>
                        </div>
                        <div class="form-group">
                            <label for="create-activity-end-time">结束时间</label>
                            <input type="datetime-local" name="end_time" id="create-activity-end-time" required>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn">创建活动</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 编辑活动模态框 -->
        <div id="edit-activity-modal" class="modal" style="display: none;">
            <div class="modal-content" style="width: 800px; max-width: 90vw;">
                <button onclick="closeModal('edit-activity-modal')" class="modal-close-btn">×</button>
                <div class="modal-header">
                    <h3>编辑活动</h3>
                </div>
                <div class="modal-body">
                    <form id="edit-activity-form">
                        <div class="form-group">
                            <label for="edit-activity-id">活动ID</label>
                            <input type="number" name="activity_id" id="edit-activity-id" required readonly>
                        </div>
                        <div class="form-group">
                            <label for="edit-activity-name">活动名称</label>
                            <input type="text" name="activity_name" id="edit-activity-name" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-activity-url">活动链接</label>
                            <div style="display: flex; gap: 8px; align-items: center;">
                                <input type="text" name="activity_url" id="edit-activity-url">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="edit-activity-status">状态</label>
                            <select name="status" id="edit-activity-status" required>
                                <option value="0">草稿</option>
                                <option value="1">活跃</option>
                                <option value="2">已结束</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-activity-priority">优先级 (0-10)</label>
                            <input type="number" name="priority" id="edit-activity-priority" min="0" max="10" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-activity-service-type">服务类型</label>
                            <select name="service_type" id="edit-activity-service-type" style="width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px; background: #ffffff;">
                                <option value="0">正式服</option>
                                <option value="1" selected="">怀旧服</option>
                                <option value="2">巅峰服</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-activity-begin-time">开始时间</label>
                            <input type="datetime-local" name="begin_time" id="edit-activity-begin-time" required>
                        </div>
                        <div class="form-group">
                            <label for="edit-activity-end-time">结束时间</label>
                            <input type="datetime-local" name="end_time" id="edit-activity-end-time" required>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn">保存修改</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- AMS链接导入模态框 -->
        <div id="import-ams-modal" class="modal" style="display: none;">
            <div class="modal-content" style="width: 600px; max-width: 90vw;">
                <button onclick="closeModal('import-ams-modal')" class="modal-close-btn">×</button>
                <div class="modal-header">
                    <h3>导入AMS链接</h3>
                </div>
                <div class="modal-body">
                    <form id="import-ams-form">
                        <div class="form-group">
                            <textarea name="ams_url" id="ams-url" rows="5" required placeholder="请粘贴AMS链接，支持多行输入（每行一个链接）：&#10;https://comm.ams.game.qq.com/ide/...&#10;https://comm.ams.game.qq.com/ide/..."></textarea>
                            <small style="color: #718096; font-size: 12px; margin-top: 5px; display: block;">
                                支持同时输入多个AMS链接，每行一个。解析失败的链接不会影响其他链接的处理。
                            </small>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn">解析并创建草稿</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 流程管理模态框 -->
        <div id="manage-flows-modal" class="modal" style="display: none;">
            <div class="modal-content" style="width: 1000px; max-width: 95vw;">
                <button onclick="closeModal('manage-flows-modal')" class="modal-close-btn">×</button>
                <div class="modal-header">
                    <h3>流程管理 - <span id="flow-activity-name"></span></h3>
                </div>
                <div class="modal-body">
                    <div id="flows-content">
                        <table id="flows-table" style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                            <thead style="background: #f7fafc;">
                                <tr>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">流程ID</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">流程名称</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">分组</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="flows-table-body">
                            </tbody>
                        </table>
                    </div>
                    <div id="create-flow-container" style="margin-top: 15px;">
                        <!-- 新建流程按钮将在这里动态添加 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 创建/编辑流程模态框 -->
        <div id="flow-edit-modal" class="modal" style="display: none;">
            <div class="modal-content" style="width: 900px; max-width: 95vw;">
                <button onclick="closeModal('flow-edit-modal')" class="modal-close-btn">×</button>
                <div class="modal-header">
                    <h3 id="flow-edit-title">新建流程</h3>
                </div>
                <div class="modal-body">
                    <form id="flow-edit-form">
                        <input type="hidden" name="flow_db_id" id="flow-db-id">
                        <input type="hidden" name="activity_id" id="flow-activity-id">
                        <input type="hidden" name="sort_order" id="flow-sort-order">

                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                            <div class="form-group">
                                <label for="flow-id">流程ID</label>
                                <input type="text" name="flow_id" id="flow-id" required placeholder="例如: 420402">
                            </div>
                            <div class="form-group">
                                <label for="flow-name">流程名称</label>
                                <input type="text" name="flow_name" id="flow-name" required placeholder="例如: 野外boss抽奖">
                            </div>
                            <div class="form-group">
                                <label for="flow-group">分组 <span style="color: #666; font-size: 12px;">(同组流程需手动选择)</span></label>
                                <div style="display: flex; gap: 8px; align-items: center;">
                                    <input type="number" name="group" id="flow-group" value="0" min="0" placeholder="0表示无分组"
                                           style="flex: 1;" onchange="updateGroupPreview()">
                                    <span id="group-preview" style="font-size: 12px; color: #666; min-width: 80px;"></span>
                                </div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                            <div class="form-group">
                                <label for="ide-token">IDE令牌</label>
                                <input type="text" name="ide_token" id="ide-token" required placeholder="32位IDE令牌">
                            </div>
                            <div class="form-group">
                                <label for="flow-status">状态</label>
                                <input type="number" name="status" id="flow-status" value="1" style="display: none;">
                                <div style="display: flex; gap: 10px; align-items: center;">
                                    <!-- 启用/禁用按钮 -->
                                    <button type="button" class="btn" id="toggle-flow-btn" onclick="toggleFlowButtonStatus()" style="background: #10b981;">
                                        已启用
                                    </button>
                                </div>
                            </div>
                        </div>


                        <div class="form-group">
                            <label>参数配置</label>

                            <!-- 流程参数 -->
                            <div style="margin-bottom: 20px;">
                                <div id="fixed-parameters-container">
                                    <div id="parameters-list">
                                        <!-- 参数将在这里动态添加 -->
                                    </div>
                                </div>

                                <!-- 增加参数按钮 -->
                                <div id="user-parameters-container">
                                    <div class="parameters-empty" onclick="addParameterRow()" 
                                        style="padding: 15px 10px;text-align: center;color: #718096;border: 2px dashed #e2e8f0;border-radius: 6px;cursor: pointer;transition: all 0.2s ease;background: #f8f9fa;" 
                                        onmouseover="this.style.borderColor='#cbd5e0'; this.style.background='#f1f5f9';"
                                        onmouseout="this.style.borderColor='#e2e8f0'; this.style.background='#f8f9fa';">
                                        <div style="font-weight: 500; margin-bottom: 3px;">增加参数配置</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <!-- 复制按钮 -->
                            <button type="button" class="btn" id="copy-flow-btn" onclick="copyCurrentFlow()"
                                    style="background: #6366f1; margin-right: 5px; display: none;">复制流程
                            </button>
                            <!-- 测试按钮 -->
                            <button type="button" class="btn" id="test-flow-btn" onclick="testCurrentFlow()"
                                    style="background: #f59e0b; margin-right: 5px; display: none;">测试流程
                            </button>
                            <!-- 保存按钮 -->
                            <button type="submit" class="btn">保存流程</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>

{{end}}

{{define "page_scripts"}}
<script src="/static/js/admin.js?v=20250711-2"></script>
<script src="/static/js/activity.js?v=20250723-2"></script>
{{end}}
