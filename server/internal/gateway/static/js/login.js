document.getElementById('loginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const errorDiv = document.getElementById('errorMessage');
    const loginBtn = document.getElementById('loginBtn');
    
    // 简单的客户端验证
    if (!username || !password) {
        showError('请输入用户名和密码');
        return;
    }
    
    // 禁用登录按钮
    loginBtn.disabled = true;
    loginBtn.textContent = '登录中...';
    
    // 发送登录请求
    fetch('/admin/api/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            username: username,
            password: password
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            window.location.href = '/admin';
        } else {
            showError(data.message || '登录失败');
        }
    })
    .catch(error => {
        console.error('登录请求失败:', error);
        showError('网络错误，请稍后重试');
    })
    .finally(() => {
        // 恢复登录按钮
        loginBtn.disabled = false;
        loginBtn.textContent = '登录';
    });
});

function showError(message) {
    const errorDiv = document.getElementById('errorMessage');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
    
    // 3秒后隐藏错误消息
    setTimeout(() => {
        errorDiv.style.display = 'none';
    }, 3000);
}

// 回车键登录
document.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        document.getElementById('loginForm').dispatchEvent(new Event('submit'));
    }
});
