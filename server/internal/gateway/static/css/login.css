* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
    background: #f0f8ff;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(43, 108, 176, 0.1);
    width: 100%;
    max-width: 400px;
    border: 1px solid #e2e8f0;
}

.login-header {
    text-align: center;
    margin-bottom: 32px;
}

.login-header h1 {
    color: #2b6cb0;
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.login-header p {
    color: #718096;
    font-size: 0.875rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #4a5568;
    font-weight: 500;
    font-size: 0.875rem;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #2b6cb0;
    box-shadow: 0 0 0 3px rgba(43, 108, 176, 0.1);
}

.login-btn {
    width: 100%;
    background: #2b6cb0;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.login-btn:hover {
    background: #2c5282;
}

.login-btn:disabled {
    background: #a0aec0;
    cursor: not-allowed;
}

.error-message {
    color: #e53e3e;
    text-align: center;
    margin-top: 16px;
    font-size: 0.875rem;
    display: none;
    padding: 8px;
    background: #fed7d7;
    border-radius: 6px;
    border: 1px solid #feb2b2;
}
