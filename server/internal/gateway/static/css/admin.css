* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f7fafc;
    color: #2d3748;
    line-height: 1.6;
}

.header {
    background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 50%, #b3dbff 100%);
    border-bottom: 1px solid rgba(43, 108, 176, 0.1);
    padding: 0;
    margin-bottom: 30px;
    box-shadow: 0 1px 3px rgba(43, 108, 176, 0.08);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
    min-height: 70px;
}

.logo {
    color: #2b6cb0;
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.nav {
    display: flex;
    gap: 2px;
    align-items: center;
}

.nav-item {
    padding: 12px 20px;
    text-decoration: none;
    color: #4a5568;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
    position: relative;
    margin: 0 2px;
}

.nav-item:hover {
    color: #2b6cb0;
    background: rgba(43, 108, 176, 0.08);
    transform: translateY(-1px);
}

.nav-item.active {
    color: #2b6cb0;
    background: rgba(43, 108, 176, 0.12);
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(43, 108, 176, 0.1);
}

.nav-item.logout {
    color: #e53e3e;
    margin-left: 16px;
    border: 1px solid rgba(229, 62, 62, 0.2);
    background: rgba(229, 62, 62, 0.05);
}

.nav-item.logout:hover {
    color: white;
    background: #e53e3e;
    border-color: #e53e3e;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(229, 62, 62, 0.2);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

.content {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    width: 100%;
    box-sizing: border-box;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e2e8f0;
    min-height: 40px;
}

.section-header h2 {
    color: #2d3748;
    font-size: 24px;
    font-weight: 600;
}

.date-picker input {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
}

.dashboard {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background: white;
    border-radius: 8px;
    padding: 24px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(43, 108, 176, 0.1);
}

.card h3 {
    color: #4a5568;
    margin-bottom: 12px;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #2b6cb0;
    margin-bottom: 4px;
}

.stat-label {
    color: #718096;
    font-size: 0.875rem;
}

.loading {
    text-align: center;
    color: #718096;
}





.search-box {
    display: flex;
    gap: 8px;
    align-items: center;
}

.search-box input {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.875rem;
}

.date-picker input {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.875rem;
}

.btn {
    background: #2b6cb0;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:hover {
    background: #2c5282;
}

.btn-secondary {
    background: #ebf8ff;
    color: #2b6cb0;
    border: 1px solid #bee3f8;
}

.btn-secondary:hover {
    background: #bee3f8;
}

.btn-success {
    background: #38a169;
    color: white;
}

.btn-success:hover {
    background: #2f855a;
}

.btn-danger {
    background: #e53e3e;
    color: white;
}

.btn-danger:hover {
    background: #c53030;
}

.btn-small {
    padding: 4px 8px;
    font-size: 0.75rem;
}

.btn-purple {
    background: #805ad5;
    color: white;
}

.btn-purple:hover {
    background: #6b46c1;
}

/* 功能按钮组样式 */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.section-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: flex-end;
    margin-left: auto;
    flex-shrink: 0; /* 防止按钮被压缩 */
    white-space: nowrap; /* 防止按钮文本换行 */
}



.modal-content h3 {
    margin: 0 0 20px 0;
    color: #2d3748;
    font-size: 1.25rem;
    font-weight: 600;
}



.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.warning-box {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.warning-box h4 {
    margin: 0 0 10px 0;
    color: #856404;
    font-size: 14px;
}

.warning-box p {
    margin: 0;
    color: #856404;
    font-size: 14px;
    line-height: 1.5;
}

.captcha-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.captcha-display {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 10px;
    font-family: monospace;
    font-size: 18px;
    font-weight: bold;
    color: #2d3748;
    letter-spacing: 3px;
    min-width: 100px;
    text-align: center;
}

.captcha-container input {
    flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        padding: 0 20px;
        min-height: 60px;
        flex-direction: column;
        gap: 12px;
        padding-top: 12px;
        padding-bottom: 12px;
    }

    .logo {
        font-size: 1.25rem;
    }

    .nav {
        flex-wrap: wrap;
        gap: 4px;
        justify-content: center;
    }

    .nav-item {
        padding: 8px 12px;
        font-size: 13px;
        margin: 0 1px;
    }

    .nav-item.logout {
        margin-left: 8px;
    }

    .container {
        padding: 15px;
    }

    .dashboard {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 10px;
        align-items: center;
        min-height: 40px;
    }

    .content {
        padding: 20px;
    }
}





.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 16px;
    table-layout: fixed;
}

#announcements-content .table,
#users-content .table,
#cdkeys-content .table,
#groups-content .table,
#admins-content .table {
    width: 100%;
}

.table th,
.table td {
    padding: 12px;
    text-align: center;
    border-bottom: 1px solid #e2e8f0;
    font-size: 0.875rem;
}

.table th,
table th {
    background: #f7fafc;
    font-weight: 600;
    color: #4a5568;
    text-align: center !important;
}

.table tbody tr:hover {
    background: #f7fafc;
}

.status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-published {
    background: #c6f6d5;
    color: #22543d;
}

.status-draft {
    background: #fed7d7;
    color: #742a2a;
}

.status-offline {
    background: #feebc8;
    color: #c05621;
}

.status-unknown {
    background: #e2e8f0;
    color: #4a5568;
}

.status-active {
    background: #c6f6d5;
    color: #22543d;
}

.status-inactive {
    background: #fed7d7;
    color: #742a2a;
}



/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    position: relative;
    display: flex;
    flex-direction: column;
}

/* 模态框头部 - 固定不滚动 */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0 24px;
    flex-shrink: 0;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 0;
}

/* 模态框主体内容 - 可滚动 */
.modal-body {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
}

/* 模态框底部 - 固定不滚动 */
.modal-footer {
    padding: 0 24px 24px 24px;
    flex-shrink: 0;
    border-top: 1px solid #e2e8f0;
    margin-top: 0;
}

/* 关闭按钮样式 */
.modal-close-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    background: none;
    border: none;
    font-size: 24px;
    color: #718096;
    cursor: pointer;
    padding: 8px;
    line-height: 1;
    border-radius: 6px;
    transition: all 0.2s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.modal-close-btn:hover {
    background: #f7fafc;
    color: #2d3748;
}

/* 模态框内部滚动容器样式 */
.modal-scroll-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
}

.modal-scroll-container::-webkit-scrollbar {
    width: 8px;
}

.modal-scroll-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.modal-scroll-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.modal-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.modal-header h3 {
    margin: 0;
    color: #2d3748;
    font-size: 1.25rem;
    font-weight: 600;
    padding-right: 40px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #718096;
}

.modal-close,
.modal-close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #718096;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    position: absolute;
    top: 15px;
    right: 15px;
}

.modal-close:hover,
.modal-close-btn:hover {
    background-color: #f7fafc;
    color: #2d3748;
}

/* 参数配置样式 */
.parameter-row {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 10px;
    align-items: end;
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: #f8f9fa;
}

.parameter-row input,
.parameter-row textarea {
    padding: 6px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
}

.parameter-row textarea {
    resize: vertical;
    min-height: 60px;
}

.parameter-row .form-group {
    margin-bottom: 0;
}

.parameter-row .form-group label {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 4px;
}

.parameter-row .remove-btn {
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 12px;
    height: 32px;
    align-self: end;
    margin-bottom: 0;
}

.parameter-row .remove-btn:hover {
    background: #dc2626;
}

.parameters-empty {
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 20px;
    border: 2px dashed #d1d5db;
    border-radius: 6px;
    margin-bottom: 10px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #4a5568;
    font-weight: 500;
    font-size: 0.875rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #2b6cb0;
    box-shadow: 0 0 0 3px rgba(43, 108, 176, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* 生成口令码模态框专用样式 */
.form-grid {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    align-items: start;
}

.form-row .form-group {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #374151;
    font-weight: 500;
    font-size: 14px;
}

.form-group input:not([type="checkbox"]):not([type="radio"]),
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background-color: #ffffff;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:not([type="checkbox"]):not([type="radio"]):focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input[type="checkbox"],
.form-group input[type="radio"] {
    width: auto;
    margin-right: 8px;
}


.form-group small {
    display: block;
    margin-top: 4px;
    color: #6b7280;
    font-size: 12px;
}

.prefix-suggestions {
    margin-top: 6px;
    font-size: 12px;
    color: #6b7280;
}

.prefix-suggestions span {
    margin-right: 8px;
}

.prefix-suggestions a {
    display: inline-block;
    margin-right: 8px;
    padding: 2px 6px;
    background-color: #f3f4f6;
    color: #3b82f6;
    text-decoration: none;
    border-radius: 3px;
    font-size: 11px;
    transition: background-color 0.2s ease;
}

.prefix-suggestions a:hover {
    background-color: #e5e7eb;
    color: #1d4ed8;
}

.form-divider {
    height: 1px;
    background: #e5e7eb;
    margin: 20px 0;
}

.security-verification {
    margin: 15px 0 0 0;
    padding: 0;
    background: none;
    border: none;
    box-shadow: none;
}

.security-header {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 16px;
    text-align: center;
}

.security-title {
    color: #92400e;
    font-size: 16px;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(146, 64, 14, 0.1);
}

.security-desc {
    color: #a16207;
    font-size: 13px;
    font-weight: 500;
    opacity: 0.9;
}

.captcha-row {
    display: flex;
    align-items: center;
    gap: 12px;
}

.captcha-submit-row {
    display: flex;
    align-items: center;
    gap: 12px;
    background: #f8fafc;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.captcha-display {
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    padding: 8px 12px;
    font-family: 'Courier New', monospace;
    font-size: 16px;
    font-weight: bold;
    color: #374151;
    letter-spacing: 2px;
    min-width: 100px;
    text-align: center;
    user-select: none;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.captcha-display:hover {
    border-color: #3b82f6;
}



.captcha-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
    text-align: center;
    color: #374151;
    background: #ffffff;
    transition: border-color 0.2s ease;
}

.captcha-input:focus {
    outline: none;
    border-color: #3b82f6;
}

.captcha-input::placeholder {
    color: #9ca3af;
    font-family: system-ui, -apple-system, sans-serif;
    letter-spacing: normal;
    font-weight: normal;
}

.submit-btn {
    padding: 12px 32px;
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.submit-btn:hover {
    background-color: #2563eb;
}

.submit-btn-inline {
    padding: 8px 16px;
    background: #10b981;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    white-space: nowrap;
    min-width: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.submit-btn-inline:hover {
    background: #059669;
}

.btn-icon {
    font-size: 14px;
}

.btn-text {
    font-size: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .captcha-row {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .captcha-submit-row {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
        padding: 10px;
    }

    .captcha-display {
        min-width: auto;
        padding: 10px;
        font-size: 16px;
    }

    .captcha-input {
        padding: 10px;
        font-size: 16px;
    }

    .submit-btn-inline {
        margin-top: 8px;
        width: 100%;
        padding: 16px 24px;
        font-size: 16px;
    }

    .security-header {
        margin-bottom: 12px;
    }

    .security-title {
        font-size: 15px;
    }

    .security-desc {
        font-size: 12px;
    }
}

/* 前缀建议样式 */
.prefix-suggestion {
    color: #0066cc;
    text-decoration: none;
    padding: 2px 4px;
    border-radius: 3px;
    background-color: #f0f8ff;
    margin: 0 2px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.prefix-suggestion:hover {
    background-color: #0066cc;
    color: white;
    text-decoration: none;
}

#prefix-suggestions {
    margin-top: 5px;
    font-size: 12px;
    color: #0066cc;
}

/* 分页按钮样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 20px;
    padding: 20px 0;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    background: white;
    color: #4a5568;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.pagination button:hover {
    background: #f7fafc;
    border-color: #2b6cb0;
    color: #2b6cb0;
}

.pagination button.active {
    background: #2b6cb0;
    color: white;
    border-color: #2b6cb0;
}

.pagination button:disabled {
    background: #f7fafc;
    color: #a0aec0;
    cursor: not-allowed;
    border-color: #e2e8f0;
}

.pagination span {
    color: #718096;
    font-size: 14px;
}


