package handler

import (
	"net/http"
	"strconv"

	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"
)

// AdminStatsHandler 管理后台统计处理器
type AdminStatsHandler struct {
	statsService *service.StatsService
	authService  *service.AuthService
}

// NewAdminStatsHandler 创建管理后台统计处理器
func NewAdminStatsHandler(
	statsService *service.StatsService,
	authService *service.AuthService,
) *AdminStatsHandler {
	return &AdminStatsHandler{
		statsService: statsService,
		authService:  authService,
	}
}

// 处理获取统计信息请求
func (h *AdminStatsHandler) HandleGetStats(w http.ResponseWriter, r *http.Request) {
	stats, err := h.statsService.GetStats()
	if err != nil {
		logger.Error("获取统计信息失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取统计信息失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取统计信息成功", stats)
}

// 处理获取历史统计信息请求
func (h *AdminStatsHandler) HandleGetHistoryStats(w http.ResponseWriter, r *http.Request) {
	// 获取查询参数
	days := r.URL.Query().Get("days")
	if days == "" {
		days = "7" // 默认7天
	}

	daysInt, _ := strconv.Atoi(days)
	if daysInt <= 0 {
		daysInt = 7
	}
	historyStats, err := h.statsService.GetHistoryStats("daily", daysInt)
	if err != nil {
		logger.Error("获取历史统计信息失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取历史统计信息失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取历史统计信息成功", historyStats)
}

// 处理仪表板数据请求
func (h *AdminStatsHandler) HandleDashboard(w http.ResponseWriter, r *http.Request) {
	// 获取仪表板数据
	dashboardData := make(map[string]interface{})

	// 获取基础统计信息
	stats, err := h.statsService.GetStats()
	if err != nil {
		logger.Error("获取统计信息失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取仪表板数据失败", nil)
		return
	}
	dashboardData["stats"] = stats

	// 获取7天历史数据
	historyStats, err := h.statsService.GetHistoryStats("daily", 7)
	if err != nil {
		logger.Error("获取历史统计信息失败: %v", err)
		// 历史数据获取失败不影响整体响应，只记录错误
		dashboardData["history_stats"] = nil
	} else {
		dashboardData["history_stats"] = historyStats
	}

	SendJSONResponse(w, http.StatusOK, "获取仪表板数据成功", dashboardData)
}

// 处理获取服务器状态请求
func (h *AdminStatsHandler) HandleGetServerStatus(w http.ResponseWriter, r *http.Request) {
	serverStatus, err := h.statsService.GetServerStatus()
	if err != nil {
		logger.Error("获取服务器状态失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取服务器状态失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取服务器状态成功", serverStatus)
}

// 处理获取在线用户统计请求
func (h *AdminStatsHandler) HandleGetOnlineUsers(w http.ResponseWriter, r *http.Request) {
	onlineUsers, err := h.statsService.GetOnlineUsers()
	if err != nil {
		logger.Error("获取在线用户统计失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取在线用户统计失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取在线用户统计成功", onlineUsers)
}

// 处理获取错误日志统计请求
func (h *AdminStatsHandler) HandleGetErrorStats(w http.ResponseWriter, r *http.Request) {
	// 获取查询参数
	hours := r.URL.Query().Get("hours")
	if hours == "" {
		hours = "24" // 默认24小时
	}

	errorStats, err := h.statsService.GetErrorStats(hours)
	if err != nil {
		logger.Error("获取错误日志统计失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取错误日志统计失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取错误日志统计成功", errorStats)
}

// 处理获取API调用统计请求
func (h *AdminStatsHandler) HandleGetAPIStats(w http.ResponseWriter, r *http.Request) {
	// 获取查询参数
	period := r.URL.Query().Get("period")
	if period == "" {
		period = "hour" // 默认按小时统计
	}

	apiStats, err := h.statsService.GetAPIStats(period)
	if err != nil {
		logger.Error("获取API调用统计失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取API调用统计失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取API调用统计成功", apiStats)
}
