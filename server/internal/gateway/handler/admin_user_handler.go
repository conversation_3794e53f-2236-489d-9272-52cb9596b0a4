package handler

import (
	"encoding/json"
	"net/http"
	"strconv"

	gatewayService "udp-server/server/internal/gateway/service"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"

	"github.com/gorilla/mux"
)

// AdminUserHandler 管理后台用户处理器
type AdminUserHandler struct {
	userAdminService  *gatewayService.UserAdminService
	cdkeyAdminService *gatewayService.CDKeyAdminService
	adminService      *gatewayService.AdminService
	authService       *service.AuthService
	permissionService *service.PermissionService
}

// NewAdminUserHandler 创建管理后台用户处理器
func NewAdminUserHandler(
	userAdminService *gatewayService.UserAdminService,
	cdkeyAdminService *gatewayService.CDKeyAdminService,
	adminService *gatewayService.AdminService,
	authService *service.AuthService,
	permissionService *service.PermissionService,
) *AdminUserHandler {
	return &AdminUserHandler{
		userAdminService:  userAdminService,
		cdkeyAdminService: cdkeyAdminService,
		adminService:      adminService,
		authService:       authService,
		permissionService: permissionService,
	}
}

// 处理获取用户列表请求
func (h *AdminUserHandler) HandleGetUsers(w http.ResponseWriter, r *http.Request) {
	// 获取查询参数
	page := r.URL.Query().Get("page")
	pageSize := r.URL.Query().Get("page_size")
	search := r.URL.Query().Get("search")
	status := r.URL.Query().Get("status")

	// 设置默认值
	if page == "" {
		page = "1"
	}
	if pageSize == "" {
		pageSize = "20"
	}

	pageInt, err := strconv.Atoi(page)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的页码", nil)
		return
	}

	pageSizeInt, err := strconv.Atoi(pageSize)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的页面大小", nil)
		return
	}

	users, err := h.userAdminService.GetUsers(pageInt, pageSizeInt, search, status, h.authService, h.permissionService)
	if err != nil {
		logger.Error("获取用户列表失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取用户列表失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取用户列表成功", users)
}

// 处理获取用户详情请求
func (h *AdminUserHandler) HandleGetUser(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	// 将字符串转换为uint64
	qqNumber, err := strconv.ParseUint(vars["qq"], 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的QQ号格式", nil)
	}

	// 使用AdminService获取用户详情（包含权限信息）
	user, err := h.userAdminService.GetUser(qqNumber, h.authService)
	if err != nil {
		logger.Error("获取用户详情失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取用户详情失败", nil)
		return
	}

	if user == nil {
		SendJSONResponse(w, http.StatusNotFound, "用户不存在", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取用户详情成功", user)
}

// 处理更新用户状态请求
func (h *AdminUserHandler) HandleUpdateUserStatus(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	qqNumber := vars["qq"]

	// 解析请求体
	var req struct {
		Status int `json:"status"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析更新用户状态请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 转换QQ号为数字
	uin, err := strconv.ParseUint(qqNumber, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的QQ号", nil)
		return
	}

	// 更新用户状态
	err = h.userAdminService.UpdateUserStatus(uin, req.Status)
	if err != nil {
		logger.Error("更新用户状态失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "更新用户状态失败", nil)
		return
	}

	logger.Info("用户状态更新成功: QQ=%s, 状态=%d", qqNumber, req.Status)
	SendJSONResponse(w, http.StatusOK, "用户状态更新成功", nil)
}

// 处理踢出用户请求
func (h *AdminUserHandler) HandleKickUser(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	vars := mux.Vars(r)
	uin, err := strconv.ParseUint(vars["qq"], 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的用户ID", nil)
		return
	}

	// 解析请求体
	var req struct {
		Reason string `json:"reason"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析踢出用户请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 踢出用户
	err = h.userAdminService.KickUser(uin, req.Reason)
	if err != nil {
		logger.Error("踢出用户失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "踢出用户失败", nil)
		return
	}

	logger.Info("用户被踢出: QQ=%d, 原因=%s", uin, req.Reason)
	SendJSONResponse(w, http.StatusOK, "用户已被踢出", nil)
}

// 处理获取用户登录历史请求
func (h *AdminUserHandler) HandleGetUserLoginHistory(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	uin, err := strconv.ParseUint(vars["qq"], 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的用户ID", nil)
		return
	}

	// 获取查询参数
	limit := r.URL.Query().Get("limit")
	if limit == "" {
		limit = "50" // 默认50条
	}

	limitInt, err := strconv.Atoi(limit)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的限制数量", nil)
		return
	}

	loginHistory, total, err := h.userAdminService.GetUserLoginHistory(uin, 1, limitInt)
	if err != nil {
		logger.Error("获取用户登录历史失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取用户登录历史失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取用户登录历史成功", map[string]interface{}{
		"history": loginHistory,
		"total":   total,
	})
}

// 处理获取用户CDKey使用记录请求
func (h *AdminUserHandler) HandleGetUserCDKeys(w http.ResponseWriter, r *http.Request) {
	uid := r.URL.Query().Get("uid")
	if uid == "" {
		SendJSONResponse(w, http.StatusBadRequest, "缺少用户ID参数", nil)
		return
	}

	uidInt, err := strconv.ParseUint(uid, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的用户ID", nil)
		return
	}

	// 获取用户CDKey使用记录
	records, err := h.cdkeyAdminService.GetUserCDKeyRecords(uidInt)
	if err != nil {
		logger.Error("获取用户CDKey使用记录失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取用户CDKey使用记录失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取用户CDKey使用记录成功", records)
}
