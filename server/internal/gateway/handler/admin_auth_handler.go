package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	gatewayService "udp-server/server/internal/gateway/service"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"
)

// AdminAuthHandler 管理后台认证处理器
type AdminAuthHandler struct {
	adminService *gatewayService.AdminService
	authService  *service.AuthService
}

// NewAdminAuthHandler 创建管理后台认证处理器
func NewAdminAuthHandler(
	adminService *gatewayService.AdminService,
	authService *service.AuthService,
) *AdminAuthHandler {
	return &AdminAuthHandler{
		adminService: adminService,
		authService:  authService,
	}
}

// 处理管理员登录请求
func (h *AdminAuthHandler) HandleLogin(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	clientIP := r.RemoteAddr
	if forwardedFor := r.Header.Get("X-Forwarded-For"); forwardedFor != "" {
		clientIP = forwardedFor
	}

	// 检查IP登录失败次数
	cache := h.authService.GetCache()
	if cache != nil {
		failedKey := fmt.Sprintf("admin_login_failed:%s", clientIP)
		var failedCount int64
		if err := cache.Get(failedKey, &failedCount); err == nil {
			if failedCount >= 5 { // 最多允许5次失败
				// 检查是否还在锁定期内
				lockKey := fmt.Sprintf("admin_login_lock:%s", clientIP)
				var lockTime int64
				if err := cache.Get(lockKey, &lockTime); err == nil {
					if time.Now().Unix() < lockTime {
						remainingTime := lockTime - time.Now().Unix()
						logger.Warn("管理员登录被锁定: IP=%s, 剩余时间=%d秒", clientIP, remainingTime)
						SendJSONResponse(w, http.StatusTooManyRequests,
							fmt.Sprintf("登录失败次数过多，请等待%d秒后重试", remainingTime), nil)
						return
					}
				}
			}
		}
	}

	// 解析请求体
	var req struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析登录请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if req.Username == "" || req.Password == "" {
		SendJSONResponse(w, http.StatusBadRequest, "用户名和密码不能为空", nil)
		return
	}

	// 验证管理员登录
	admin, err := h.adminService.ValidateAdminLogin(req.Username, req.Password)
	if err != nil {
		logger.Error("管理员登录验证失败: %v", err)

		// 记录登录失败次数
		if cache != nil {
			failedKey := fmt.Sprintf("admin_login_failed:%s", clientIP)
			var failedCount int64
			cache.Get(failedKey, &failedCount)
			failedCount++

			// 设置失败次数，过期时间1小时
			cache.Set(failedKey, failedCount, time.Hour)

			// 如果失败次数达到5次，锁定30分钟
			if failedCount >= 5 {
				lockKey := fmt.Sprintf("admin_login_lock:%s", clientIP)
				lockTime := time.Now().Add(30 * time.Minute).Unix()
				cache.Set(lockKey, lockTime, 30*time.Minute)
				logger.Warn("管理员登录IP被锁定: IP=%s, 失败次数=%d", clientIP, failedCount)
			}
		}

		SendJSONResponse(w, http.StatusUnauthorized, "用户名或密码错误", nil)
		return
	}

	// 登录成功，清除失败记录
	if cache != nil {
		failedKey := fmt.Sprintf("admin_login_failed:%s", clientIP)
		lockKey := fmt.Sprintf("admin_login_lock:%s", clientIP)
		cache.Delete(failedKey)
		cache.Delete(lockKey)
	}

	// 生成管理员令牌
	token, err := h.adminService.GenerateAdminToken(admin.UID)
	if err != nil {
		logger.Error("生成管理员令牌失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "登录失败", nil)
		return
	}

	// 设置Cookie
	cookie := &http.Cookie{
		Name:     "admin_token",
		Value:    token,
		Path:     "/",
		HttpOnly: true,
		Secure:   true,
		SameSite: http.SameSiteLaxMode,
		Expires:  time.Now().Add(24 * time.Hour),
	}
	http.SetCookie(w, cookie)

	// 记录登录日志
	logger.Info("管理员登录成功: 用户名=%s, UID=%d", req.Username, admin.UID)
	SendJSONResponse(w, http.StatusOK, "登录成功", admin)
}

// 处理管理员登出请求
func (h *AdminAuthHandler) HandleLogout(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	// 获取当前token
	token := ""
	if cookie, err := r.Cookie("admin_token"); err == nil {
		token = cookie.Value
	}

	if token != "" {
		// 从缓存中删除token
		cache := h.authService.GetCache()
		if cache != nil {
			cacheKey := "admin_token:" + token
			cache.Delete(cacheKey)
		}
	}

	// 清除Cookie
	cookie := &http.Cookie{
		Name:     "admin_token",
		Value:    "",
		Path:     "/",
		HttpOnly: true,
		Expires:  time.Now().Add(-1 * time.Hour),
	}
	http.SetCookie(w, cookie)

	logger.Info("管理员登出成功")
	SendJSONResponse(w, http.StatusOK, "登出成功", nil)
}
