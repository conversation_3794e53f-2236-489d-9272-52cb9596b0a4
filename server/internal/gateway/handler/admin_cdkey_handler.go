package handler

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	gatewayService "udp-server/server/internal/gateway/service"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"

	"github.com/gorilla/mux"
)

// AdminCDKeyHandler 管理后台CDKEY处理器
type AdminCDKeyHandler struct {
	cdkeyAdminService *gatewayService.CDKeyAdminService
	authService       *service.AuthService
	cdkeyService      *service.CDKeyService
}

// NewAdminCDKeyHandler 创建管理后台CDKEY处理器
func NewAdminCDKeyHandler(
	cdkeyAdminService *gatewayService.CDKeyAdminService,
	authService *service.AuthService,
	cdkeyService *service.CDKeyService,
) *AdminCDKeyHandler {
	return &AdminCDKeyHandler{
		cdkeyAdminService: cdkeyAdminService,
		authService:       authService,
		cdkeyService:      cdkeyService,
	}
}

// 处理获取CDKEY列表请求
func (h *AdminCDKeyHandler) HandleGetCDKeys(w http.ResponseWriter, r *http.Request) {
	// 获取查询参数
	page := r.URL.Query().Get("page")
	pageSize := r.URL.Query().Get("page_size")
	cdkeyType := r.URL.Query().Get("type")
	status := r.URL.Query().Get("status")
	search := r.URL.Query().Get("search")

	// 设置默认值
	if page == "" {
		page = "1"
	}
	if pageSize == "" {
		pageSize = "20"
	}

	pageInt, err := strconv.Atoi(page)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的页码", nil)
		return
	}

	pageSizeInt, err := strconv.Atoi(pageSize)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的页面大小", nil)
		return
	}

	cdkeys, err := h.cdkeyAdminService.GetCDKeys(pageInt, pageSizeInt, cdkeyType, status, search)
	if err != nil {
		logger.Error("获取CDKEY列表失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取CDKEY列表失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "success", cdkeys)
}

// 处理获取CDKEY详情请求
func (h *AdminCDKeyHandler) HandleGetCDKey(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	cdkeyID := vars["id"]

	id, err := strconv.ParseUint(cdkeyID, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的CDKEY ID", nil)
		return
	}

	cdkey, err := h.cdkeyAdminService.GetCDKeyByID(id)
	if err != nil {
		logger.Error("获取CDKEY详情失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取CDKEY详情失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取CDKEY详情成功", cdkey)
}

// 处理创建CDKEY请求
func (h *AdminCDKeyHandler) HandleCreateCDKeys(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	// 解析请求体
	var req struct {
		Count       int    `json:"count"`
		Type        string `json:"type"`
		TimeType    string `json:"time_type"`
		Duration    int    `json:"duration"`
		StartTime   string `json:"start_time"`
		EndTime     string `json:"end_time"`
		FixedTime   string `json:"fixed_time"`
		Prefix      string `json:"prefix"`
		Description string `json:"description"`
		UsageCount  int    `json:"usage_count"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析创建CDKEY请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if req.Count <= 0 || req.Count > 5000 {
		SendJSONResponse(w, http.StatusBadRequest, "CDKEY数量必须在1-5000之间", nil)
		return
	}

	if req.Type == "" {
		SendJSONResponse(w, http.StatusBadRequest, "口令码类型不能为空", nil)
		return
	}

	// 解析时间
	var startTime, endTime *time.Time
	if req.StartTime != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", req.StartTime); err == nil {
			startTime = &t
		}
	}
	if req.EndTime != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", req.EndTime); err == nil {
			endTime = &t
		}
	}

	var fixedTime *time.Time
	if req.FixedTime != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", req.FixedTime); err == nil {
			fixedTime = &t
		}
	}

	// 创建CDKEY
	cdkeys, err := h.cdkeyAdminService.CreateCDKeysForAdmin(
		req.Count,
		req.Type,
		req.TimeType,
		req.Description,
		req.Prefix,
		startTime,
		endTime,
		1, // 管理员ID，这里暂时使用1
		req.Duration,
		fixedTime,
	)
	if err != nil {
		logger.Error("创建CDKEY失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "创建CDKEY失败", nil)
		return
	}

	logger.Info("CDKEY创建成功: 数量=%d, 类型=%s", req.Count, req.Type)
	SendJSONResponse(w, http.StatusOK, "CDKEY创建成功", map[string]interface{}{
		"cdkeys": cdkeys,
		"count":  len(cdkeys),
	})
}

// 处理绑定CDKEY到用户请求
func (h *AdminCDKeyHandler) HandleBindCDKeyToUser(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	// 解析请求体
	var req struct {
		UID    uint64 `json:"uid"`
		CDKey  string `json:"cdkey"`
		Reason string `json:"reason"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析绑定CDKEY请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if req.CDKey == "" || req.UID == 0 {
		SendJSONResponse(w, http.StatusBadRequest, "口令码和用户UID不能为空", nil)
		return
	}

	// 获取当前管理员信息
	adminID := uint64(1) // 默认管理员ID
	if cookie, err := r.Cookie("admin_token"); err == nil {
		token := cookie.Value
		if token != "" {
			// 验证token并获取管理员ID
			cache := h.authService.GetCache()
			if cache != nil {
				cacheKey := "admin_token:" + token
				var adminInfo map[string]interface{}
				if err := cache.Get(cacheKey, &adminInfo); err == nil {
					if uid, exists := adminInfo["uid"]; exists {
						if uidFloat, ok := uid.(float64); ok {
							adminID = uint64(uidFloat)
						}
					}
				}
			}
		}
	}

	// 获取客户端IP
	clientIP := r.RemoteAddr
	if forwarded := r.Header.Get("X-Forwarded-For"); forwarded != "" {
		clientIP = forwarded
	}

	// 使用CDKey服务激活口令码（管理员操作）
	err := h.cdkeyService.ActivateCDKey(req.CDKey, req.UID, int64(adminID), clientIP)
	if err != nil {
		logger.Error("绑定CDKEY到用户失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "绑定口令码失败: "+err.Error(), nil)
		return
	}

	logger.Info("CDKEY绑定成功: 代码=%s, 用户UID=%d, 原因=%s", req.CDKey, req.UID, req.Reason)
	SendJSONResponse(w, http.StatusOK, "CDKEY绑定成功", nil)
}

// 处理删除CDKEY请求
func (h *AdminCDKeyHandler) HandleDeleteCDKey(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	vars := mux.Vars(r)
	cdkeyID := vars["id"]

	id, err := strconv.ParseUint(cdkeyID, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的CDKEY ID", nil)
		return
	}

	// 删除CDKEY
	err = h.cdkeyAdminService.DeleteCDKey(id)
	if err != nil {
		logger.Error("删除CDKEY失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "删除CDKEY失败", nil)
		return
	}

	logger.Info("CDKEY删除成功: ID=%d", id)
	SendJSONResponse(w, http.StatusOK, "CDKEY删除成功", nil)
}

// 处理获取CDKEY使用记录请求
func (h *AdminCDKeyHandler) HandleGetCDKeyUsage(w http.ResponseWriter, r *http.Request) {
	cdkey := r.URL.Query().Get("cdkey")
	if cdkey == "" {
		SendJSONResponse(w, http.StatusBadRequest, "缺少口令码参数", nil)
		return
	}

	// 获取使用记录
	result, err := h.cdkeyAdminService.GetCDKeyUsageRecords(cdkey)
	if err != nil {
		logger.Error("获取口令码使用记录失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取使用记录失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取使用记录成功", result)
}

// 处理获取CDKEY统计信息请求
func (h *AdminCDKeyHandler) HandleGetCDKeyStats(w http.ResponseWriter, r *http.Request) {
	stats, err := h.cdkeyAdminService.GetCDKeyStats()
	if err != nil {
		logger.Error("获取CDKEY统计信息失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取CDKEY统计信息失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取CDKEY统计信息成功", stats)
}

// 处理批量删除CDKEY请求
func (h *AdminCDKeyHandler) HandleBatchDeleteCDKeys(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	// 解析请求体
	var req struct {
		CDKeyIDs []uint64 `json:"cdkey_ids"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析批量删除CDKEY请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if len(req.CDKeyIDs) == 0 {
		SendJSONResponse(w, http.StatusBadRequest, "CDKEY ID列表不能为空", nil)
		return
	}

	// 批量删除CDKEY
	deletedCount, err := h.cdkeyAdminService.BatchDeleteCDKeys(req.CDKeyIDs)
	if err != nil {
		logger.Error("批量删除CDKEY失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "批量删除CDKEY失败", nil)
		return
	}

	logger.Info("CDKEY批量删除成功: 删除数量=%d", deletedCount)
	SendJSONResponse(w, http.StatusOK, "CDKEY批量删除成功", map[string]interface{}{
		"deleted_count": deletedCount,
	})
}
