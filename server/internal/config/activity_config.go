package config

import "time"

// ActivityConfig 活动配置结构
type ActivityConfig struct {
	StartTime        time.Time // 活动开始时间
	EndTime          time.Time // 活动结束时间
	SignInResumeTime time.Time // 签到权限恢复时间
	Title            string    // 活动标题
	Description      string    // 活动描述
}

// IsActive 检查活动是否正在进行中
func (ac *ActivityConfig) IsActive() bool {
	now := time.Now()
	return now.After(ac.StartTime) && now.Before(ac.EndTime)
}

// GetRemainingTime 获取活动剩余时间
func (ac *ActivityConfig) GetRemainingTime() time.Duration {
	now := time.Now()
	if now.After(ac.EndTime) {
		return 0
	}
	return ac.EndTime.Sub(now)
}

// GetCurrentActivityConfig 获取当前活动配置
// 这里集中管理所有活动时间配置，便于维护
// 如需修改活动时间，只需要修改这里的配置即可
func GetCurrentActivityConfig() *ActivityConfig {
	return &ActivityConfig{
		// 活动开始时间：2025年6月25日 00:00:00
		StartTime: time.Date(2025, 6, 25, 0, 0, 0, 0, time.Local),
		
		// 活动结束时间：2025年7月10日 23:59:59（15天免费体验）
		EndTime: time.Date(2025, 7, 10, 23, 59, 59, 0, time.Local),
		
		// 签到权限恢复时间：2025年7月8日 12:00:00
		SignInResumeTime: time.Date(2025, 7, 8, 12, 0, 0, 0, time.Local),
		
		// 活动信息
		Title:       "战斗统计功能限时免费体验",
		Description: "自6月25日起所有账号限时免费体验15日，7月10日起恢复连续签到3天获得3天的方式",
	}
}

// 全局活动配置实例（单例模式）
var globalActivityConfig *ActivityConfig

// GetGlobalActivityConfig 获取全局活动配置（单例）
func GetGlobalActivityConfig() *ActivityConfig {
	if globalActivityConfig == nil {
		globalActivityConfig = GetCurrentActivityConfig()
	}
	return globalActivityConfig
}

// RefreshActivityConfig 刷新活动配置（用于配置更新后重新加载）
func RefreshActivityConfig() {
	globalActivityConfig = GetCurrentActivityConfig()
}
