package cache

import (
	"context"
	"fmt"
	"sync"
	"time"

	"udp-server/server/pkg/logger"
)

// CacheStrategy 缓存策略接口
type CacheStrategy interface {
	Get(key string, value interface{}) error
	Set(key string, value interface{}, expiration time.Duration) error
	Delete(key string) error
	Clear() error
}

// MultiLevelCache 多级缓存
type MultiLevelCache struct {
	l1Cache Cache // L1: 内存缓存
	l2Cache Cache // L2: Redis缓存
	stats   *CacheStats
	mu      sync.RWMutex
}

// CacheStats 缓存统计
type CacheStats struct {
	L1Hits   int64 `json:"l1_hits"`
	L1Misses int64 `json:"l1_misses"`
	L2Hits   int64 `json:"l2_hits"`
	L2Misses int64 `json:"l2_misses"`
	Writes   int64 `json:"writes"`
	Deletes  int64 `json:"deletes"`
}

// NewMultiLevelCache 创建多级缓存
func NewMultiLevelCache(l1Cache, l2Cache Cache) *MultiLevelCache {
	return &MultiLevelCache{
		l1Cache: l1Cache,
		l2Cache: l2Cache,
		stats:   &CacheStats{},
	}
}

// Get 多级缓存获取
func (m *MultiLevelCache) Get(key string, value interface{}) error {
	start := time.Now()

	// 先尝试L1缓存
	if err := m.l1Cache.Get(key, value); err == nil {
		m.mu.Lock()
		m.stats.L1Hits++
		m.mu.Unlock()

		logger.Debug("Cache L1 hit: key=%s, duration=%v", key, time.Since(start))
		return nil
	}

	m.mu.Lock()
	m.stats.L1Misses++
	m.mu.Unlock()

	// L1未命中，尝试L2缓存
	if err := m.l2Cache.Get(key, value); err == nil {
		m.mu.Lock()
		m.stats.L2Hits++
		m.mu.Unlock()

		// 回写到L1缓存
		if setErr := m.l1Cache.Set(key, value, 5*time.Minute); setErr != nil {
			logger.Warn("回写L1缓存失败: %v", setErr)
		}

		logger.Debug("Cache L2 hit: key=%s, duration=%v", key, time.Since(start))
		return nil
	}

	m.mu.Lock()
	m.stats.L2Misses++
	m.mu.Unlock()

	logger.Debug("Cache miss: key=%s, duration=%v", key, time.Since(start))
	return fmt.Errorf("cache miss")
}

// Set 多级缓存设置
func (m *MultiLevelCache) Set(key string, value interface{}, expiration time.Duration) error {
	start := time.Now()

	// 同时写入L1和L2缓存
	var errs []error

	if err := m.l1Cache.Set(key, value, expiration); err != nil {
		errs = append(errs, fmt.Errorf("L1 cache set failed: %v", err))
	}

	if err := m.l2Cache.Set(key, value, expiration); err != nil {
		errs = append(errs, fmt.Errorf("L2 cache set failed: %v", err))
	}

	m.mu.Lock()
	m.stats.Writes++
	m.mu.Unlock()

	if len(errs) > 0 {
		logger.Error("Cache set failed: key=%s, duration=%v, errors=%v", key, time.Since(start), errs)
		return fmt.Errorf("cache set errors: %v", errs)
	}

	logger.Debug("Cache set success: key=%s, duration=%v", key, time.Since(start))
	return nil
}

// Delete 多级缓存删除
func (m *MultiLevelCache) Delete(key string) error {
	start := time.Now()

	// 同时从L1和L2缓存删除
	var errs []error

	if err := m.l1Cache.Delete(key); err != nil {
		errs = append(errs, fmt.Errorf("L1 cache delete failed: %v", err))
	}

	if err := m.l2Cache.Delete(key); err != nil {
		errs = append(errs, fmt.Errorf("L2 cache delete failed: %v", err))
	}

	m.mu.Lock()
	m.stats.Deletes++
	m.mu.Unlock()

	if len(errs) > 0 {
		logger.Error("Cache delete failed: key=%s, duration=%v, errors=%v", key, time.Since(start), errs)
		return fmt.Errorf("cache delete errors: %v", errs)
	}

	logger.Debug("Cache delete success: key=%s, duration=%v", key, time.Since(start))
	return nil
}

// GetStats 获取缓存统计
func (m *MultiLevelCache) GetStats() *CacheStats {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return &CacheStats{
		L1Hits:   m.stats.L1Hits,
		L1Misses: m.stats.L1Misses,
		L2Hits:   m.stats.L2Hits,
		L2Misses: m.stats.L2Misses,
		Writes:   m.stats.Writes,
		Deletes:  m.stats.Deletes,
	}
}

// CacheManager 缓存管理器
type CacheManager struct {
	caches map[string]Cache
	mu     sync.RWMutex
}

// NewCacheManager 创建缓存管理器
func NewCacheManager() *CacheManager {
	return &CacheManager{
		caches: make(map[string]Cache),
	}
}

// RegisterCache 注册缓存
func (cm *CacheManager) RegisterCache(name string, cache Cache) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.caches[name] = cache
}

// GetCache 获取缓存
func (cm *CacheManager) GetCache(name string) (Cache, bool) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	cache, exists := cm.caches[name]
	return cache, exists
}

// WarmupCache 缓存预热
type WarmupCache struct {
	cache    Cache
	warmupFn func() (map[string]interface{}, error)
}

// NewWarmupCache 创建预热缓存
func NewWarmupCache(cache Cache, warmupFn func() (map[string]interface{}, error)) *WarmupCache {
	return &WarmupCache{
		cache:    cache,
		warmupFn: warmupFn,
	}
}

// Warmup 执行缓存预热
func (w *WarmupCache) Warmup() error {
	start := time.Now()

	data, err := w.warmupFn()
	if err != nil {
		logger.Error("缓存预热失败: %v", err)
		return err
	}

	var successCount, failCount int
	for key, value := range data {
		if err := w.cache.Set(key, value, 1*time.Hour); err != nil {
			failCount++
			logger.Warn("预热缓存项失败: key=%s, error=%v", key, err)
		} else {
			successCount++
		}
	}

	duration := time.Since(start)
	logger.Info("缓存预热完成: 成功=%d, 失败=%d, 耗时=%v", successCount, failCount, duration)

	return nil
}

// AsyncCache 异步缓存
type AsyncCache struct {
	cache   Cache
	queue   chan CacheOperation
	workers int
	ctx     context.Context
	cancel  context.CancelFunc
}

// CacheOperation 缓存操作
type CacheOperation struct {
	Type       string
	Key        string
	Value      interface{}
	Expiration time.Duration
	Callback   func(error)
}

// NewAsyncCache 创建异步缓存
func NewAsyncCache(cache Cache, workers int) *AsyncCache {
	ctx, cancel := context.WithCancel(context.Background())

	ac := &AsyncCache{
		cache:   cache,
		queue:   make(chan CacheOperation, 1000),
		workers: workers,
		ctx:     ctx,
		cancel:  cancel,
	}

	// 启动工作协程
	for i := 0; i < workers; i++ {
		go ac.worker()
	}

	return ac
}

// worker 工作协程
func (ac *AsyncCache) worker() {
	for {
		select {
		case <-ac.ctx.Done():
			return
		case op := <-ac.queue:
			var err error
			switch op.Type {
			case "set":
				err = ac.cache.Set(op.Key, op.Value, op.Expiration)
			case "delete":
				err = ac.cache.Delete(op.Key)
			}

			if op.Callback != nil {
				op.Callback(err)
			}
		}
	}
}

// SetAsync 异步设置
func (ac *AsyncCache) SetAsync(key string, value interface{}, expiration time.Duration, callback func(error)) {
	select {
	case ac.queue <- CacheOperation{
		Type:       "set",
		Key:        key,
		Value:      value,
		Expiration: expiration,
		Callback:   callback,
	}:
	default:
		if callback != nil {
			callback(fmt.Errorf("cache queue full"))
		}
	}
}

// DeleteAsync 异步删除
func (ac *AsyncCache) DeleteAsync(key string, callback func(error)) {
	select {
	case ac.queue <- CacheOperation{
		Type:     "delete",
		Key:      key,
		Callback: callback,
	}:
	default:
		if callback != nil {
			callback(fmt.Errorf("cache queue full"))
		}
	}
}

// Close 关闭异步缓存
func (ac *AsyncCache) Close() {
	ac.cancel()
	close(ac.queue)
}

// CacheMetrics 缓存指标
type CacheMetrics struct {
	HitRate     float64 `json:"hit_rate"`
	MissRate    float64 `json:"miss_rate"`
	Operations  int64   `json:"operations"`
	Errors      int64   `json:"errors"`
	AvgLatency  float64 `json:"avg_latency_ms"`
	LastUpdated int64   `json:"last_updated"`
}

// CalculateMetrics 计算缓存指标
func CalculateMetrics(stats *CacheStats) *CacheMetrics {
	totalHits := stats.L1Hits + stats.L2Hits
	totalMisses := stats.L1Misses + stats.L2Misses
	totalOps := totalHits + totalMisses

	var hitRate, missRate float64
	if totalOps > 0 {
		hitRate = float64(totalHits) / float64(totalOps) * 100
		missRate = float64(totalMisses) / float64(totalOps) * 100
	}

	return &CacheMetrics{
		HitRate:     hitRate,
		MissRate:    missRate,
		Operations:  totalOps,
		LastUpdated: time.Now().Unix(),
	}
}
