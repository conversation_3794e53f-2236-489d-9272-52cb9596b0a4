package cache

import (
	"encoding/json"
	"errors"
	"sync"
	"time"
)

// ErrCacheMiss 缓存未命中错误
var ErrCacheMiss = errors.New("cache miss")

// Cache 缓存接口
type Cache interface {
	Get(key string, value interface{}) error
	Set(key string, value interface{}, expiration time.Duration) error
	Delete(key string) error
	Close() error
	Increment(key string, value int64) (int64, error)
	Expire(key string, expiration time.Duration) error
	// 健康检查相关方法
	HealthCheck() error
	StartHealthMonitor(interval time.Duration) error
	StopHealthMonitor() error
}

// 缓存项结构
type cacheItem struct {
	value      interface{}
	expiration int64 // Unix时间戳，0表示永不过期
}

// 是否已过期
func (item *cacheItem) isExpired() bool {
	if item.expiration == 0 {
		return false
	}
	return time.Now().UnixNano() > item.expiration
}

// cache 内存缓存实现
type cache struct {
	items     sync.Map // 存储缓存项
	janitor   *janitor // 清理过期缓存的定时器
	stop<PERSON>han  chan struct{}
	closeLock sync.Mutex
	closed    bool
}

// janitor 清理过期缓存的定时器
type janitor struct {
	interval time.Duration
	stop     chan struct{}
}

// NewCache 创建新的缓存实例
func NewCache() Cache {
	return NewMemoryCache()
}

// NewMemoryCache 创建内存缓存实例
func NewMemoryCache() Cache {
	c := &cache{
		items:    sync.Map{},
		stopChan: make(chan struct{}),
	}

	// 启动定期清理过期缓存的goroutine
	j := &janitor{
		interval: time.Minute, // 每分钟清理一次
		stop:     make(chan struct{}),
	}
	c.janitor = j

	go c.janitorRun()

	return c
}

// InitCache 根据配置初始化Redis缓存（强制使用Redis）
func InitCache(redisHost string, redisPort int, redisPassword string, redisDB int) (Cache, error) {
	// 强制使用Redis缓存，不回退到内存缓存
	return NewRedisCache(redisHost, redisPort, redisPassword, redisDB)
}

// janitorRun 运行清理过期缓存的循环
func (c *cache) janitorRun() {
	ticker := time.NewTicker(c.janitor.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.deleteExpired()
		case <-c.janitor.stop:
			return
		}
	}
}

// deleteExpired 删除所有过期的缓存项
func (c *cache) deleteExpired() {
	var expired []string

	// 找出所有过期的缓存项
	c.items.Range(func(k, v interface{}) bool {
		if item, ok := v.(*cacheItem); ok && item.isExpired() {
			expired = append(expired, k.(string))
		}
		return true
	})

	// 删除过期的缓存项
	for _, k := range expired {
		c.items.Delete(k)
	}
}

// Get 获取缓存值
func (c *cache) Get(key string, value interface{}) error {
	v, ok := c.items.Load(key)
	if !ok {
		return ErrCacheMiss
	}

	item, ok := v.(*cacheItem)
	if !ok {
		return errors.New("invalid cache item type")
	}

	// 判断是否过期
	if item.isExpired() {
		c.items.Delete(key)
		return ErrCacheMiss
	}

	// 使用json进行序列化和反序列化，以确保类型安全
	data, err := json.Marshal(item.value)
	if err != nil {
		return err
	}

	return json.Unmarshal(data, value)
}

// Set 设置缓存值
func (c *cache) Set(key string, value interface{}, expiration time.Duration) error {
	var exp int64
	if expiration > 0 {
		exp = time.Now().Add(expiration).UnixNano()
	}

	c.items.Store(key, &cacheItem{
		value:      value,
		expiration: exp,
	})

	return nil
}

// Delete 删除缓存值
func (c *cache) Delete(key string) error {
	c.items.Delete(key)
	return nil
}

// Close 关闭缓存，清理资源
func (c *cache) Close() error {
	c.closeLock.Lock()
	defer c.closeLock.Unlock()

	if !c.closed {
		close(c.janitor.stop)
		c.closed = true
	}
	return nil
}

// Increment 增加键的值（内存缓存不支持原子递增，返回错误）
func (c *cache) Increment(key string, value int64) (int64, error) {
	return 0, errors.New("increment operation not supported in memory cache")
}

// Expire 设置键的过期时间
func (c *cache) Expire(key string, expiration time.Duration) error {
	v, ok := c.items.Load(key)
	if !ok {
		return errors.New("key not found")
	}

	item, ok := v.(*cacheItem)
	if !ok {
		return errors.New("invalid cache item type")
	}

	// 更新过期时间
	var exp int64
	if expiration > 0 {
		exp = time.Now().Add(expiration).UnixNano()
	}

	item.expiration = exp
	c.items.Store(key, item)

	return nil
}

// HealthCheck 内存缓存健康检查（总是返回成功）
func (c *cache) HealthCheck() error {
	return nil
}

// StartHealthMonitor 启动健康监控（内存缓存不需要监控）
func (c *cache) StartHealthMonitor(interval time.Duration) error {
	return nil
}

// StopHealthMonitor 停止健康监控（内存缓存不需要监控）
func (c *cache) StopHealthMonitor() error {
	return nil
}
