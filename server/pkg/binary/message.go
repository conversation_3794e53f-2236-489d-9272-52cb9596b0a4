package binary

import (
	"fmt"
	"time"
)

// ==================== 创建消息 ==================== //

// Message 二进制协议消息
type Message struct {
	Header *MessageHeader
	Body   []byte
}

// NewMessage 创建新消息
func NewMessage(msgType uint8, flags uint8, body []byte) *Message {
	bodyLength := uint32(len(body))
	return &Message{
		Header: &MessageHeader{
			Magic:     ProtocolMagic,
			Version:   ProtocolVersion,
			MsgType:   msgType,
			Flags:     flags,
			Length:    HeaderSize + bodyLength,
			Timestamp: uint64(time.Now().UnixMilli()),
		},
		Body: body,
	}
}

// 克隆消息
func (m *Message) Clone() *Message {
	bodyClone := make([]byte, len(m.Body))
	copy(bodyClone, m.Body)

	return &Message{
		Header: &MessageHeader{
			Magic:     m.Header.Magic,
			Version:   m.Header.Version,
			MsgType:   m.Header.MsgType,
			Flags:     m.Header.Flags,
			Length:    m.Header.Length,
			Timestamp: m.Header.Timestamp,
		},
		Body: bodyClone,
	}
}

// 编码消息为字节流
func (m *Message) Encode() ([]byte, error) {
	// 验证消息大小
	totalLength := HeaderSize + uint32(len(m.Body))
	if totalLength > MaxMessageSize {
		return nil, fmt.Errorf("message too large: %d bytes (max: %d)", totalLength, MaxMessageSize)
	}

	// 更新消息头长度
	m.Header.Length = totalLength

	// 编码消息头
	result := m.Header.Encode()

	// 添加消息体
	result = append(result, m.Body...)

	return result, nil
}

// 编码消息并加密
func (m *Message) EncodeWithEncryption() []byte {
	// 先编码消息
	data, err := m.Encode()
	if err != nil {
		return nil
	}

	// 压缩消息体
	compressedData := CompressMessageBody(data)

	// 加密消息体
	return EncryptMessageBody(compressedData)
}

// 从字节流解码消息
func DecodeMessage(data []byte) (*Message, error) {
	if len(data) < HeaderSize {
		return nil, fmt.Errorf("insufficient data for message header: need %d bytes, got %d", HeaderSize, len(data))
	}

	// 解码消息头
	header, err := DecodeHeader(data)
	if err != nil {
		return nil, fmt.Errorf("failed to decode header: %w", err)
	}

	// 验证数据长度
	if len(data) < int(header.Length) {
		return nil, fmt.Errorf("insufficient data for complete message: need %d bytes, got %d", header.Length, len(data))
	}

	// 验证消息大小
	if header.Length > MaxMessageSize {
		return nil, fmt.Errorf("message too large: %d bytes (max: %d)", header.Length, MaxMessageSize)
	}

	// 提取消息体
	bodyLength := int(header.Length) - HeaderSize
	body := make([]byte, bodyLength)
	if bodyLength > 0 {
		copy(body, data[HeaderSize:HeaderSize+bodyLength])
	}

	message := &Message{
		Header: header,
		Body:   body,
	}

	return message, nil
}

// 解码并解密消息
func DecodeMessageWithDecryption(data []byte) (*Message, error) {
	// 先解密数据
	decryptedData, err := DecryptMessageBody(data)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt message: %w", err)
	}

	// 解码消息
	return DecodeMessage(decryptedData)
}

// 验证消息的有效性
func (m *Message) Validate() error {
	if m.Header == nil {
		return fmt.Errorf("message header is nil")
	}

	if err := m.Header.Validate(); err != nil {
		return fmt.Errorf("invalid header: %w", err)
	}

	// 验证消息体长度
	expectedBodyLength := int(m.Header.Length) - HeaderSize
	if len(m.Body) != expectedBodyLength {
		return fmt.Errorf("body length mismatch: expected %d, got %d", expectedBodyLength, len(m.Body))
	}

	return nil
}
