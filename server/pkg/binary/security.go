package binary

import (
	"fmt"
	"sync"
	"time"

	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"
)

// 消息安全验证器
type SecurityValidator struct {
	cache       cache.Cache
	timestamp   map[string]uint64 // 用户最后消息时间戳缓存 userKey -> timestamp
	timestampMu sync.RWMutex
}

// 创建安全验证器
func NewSecurityValidator(cache cache.Cache) *SecurityValidator {
	validator := &SecurityValidator{
		cache:     cache,
		timestamp: make(map[string]uint64),
	}

	logger.Info("安全验证器已启动: 基于时间戳的重放检测")
	return validator
}

// 验证消息的安全性
func (sv *SecurityValidator) ValidateMessage(header *MessageHeader, clientIP string, messageBody []byte) error {
	// 1. 验证时间戳
	if err := sv.validateTimestamp(header.Timestamp); err != nil {
		return NewProtocolError(ErrorCodeInvalidTimestamp, "")
	}

	// 2. 检查重放攻击
	if err := sv.checkReplayAttack(header, clientIP, messageBody); err != nil {
		return NewProtocolError(ErrorCodeUnauthorized, "")
	}

	return nil
}

// 验证时间戳是否在合理的时间窗口内
func (sv *SecurityValidator) validateTimestamp(timestamp uint64) error {
	// 获取当前时间戳
	now := uint64(time.Now().UnixMilli())

	// 设置时间窗口（12小时）
	timeWindow := uint64(43200000)

	// 检查时间戳是否在未来
	if timestamp > now+timeWindow {
		logger.Warn("消息时间戳过于超前: msgTime=%d, now=%d, diff=%d", timestamp, now, timestamp-now)
		return fmt.Errorf("请更新设备时间")
	}

	// 检查时间戳是否过于陈旧
	if timestamp < now-timeWindow {
		logger.Warn("消息时间戳过于陈旧: msgTime=%d, now=%d, diff=%d", timestamp, now, now-timestamp)
		return fmt.Errorf("请更新设备时间")
	}

	return nil
}

// 检查重放攻击（基于用户时间戳）
func (sv *SecurityValidator) checkReplayAttack(header *MessageHeader, clientIP string, messageBody []byte) error {
	// 生成用户标识符（IP + 消息类型）
	userKey := fmt.Sprintf("%s:%d", clientIP, header.MsgType)

	sv.timestampMu.Lock()
	defer sv.timestampMu.Unlock()

	// 获取用户上次消息的时间戳
	lastTimestamp, exists := sv.timestamp[userKey]

	// 检查时间戳是否递增
	if exists && header.Timestamp <= lastTimestamp {
		logger.Warn("检测到重放攻击 (时间戳倒退): userKey=%s, lastTimestamp=%d, currentTimestamp=%d", userKey, lastTimestamp, header.Timestamp)
		return fmt.Errorf("检测到重放攻击")
	}

	// 记录用户最后时间戳
	sv.timestamp[userKey] = header.Timestamp
	return nil
}

// 获取当前缓存大小
func (sv *SecurityValidator) GetCacheSize() int {
	sv.timestampMu.RLock()
	defer sv.timestampMu.RUnlock()
	return len(sv.timestamp)
}

// 获取安全验证器统计信息
func (sv *SecurityValidator) GetStats() map[string]interface{} {
	sv.timestampMu.RLock()
	defer sv.timestampMu.RUnlock()

	return map[string]interface{}{
		"user_cache_size": len(sv.timestamp),
		"description":     "基于时间戳的重放攻击检测，要求时间戳递增",
	}
}
