package binary

import (
	"encoding/binary"
	"fmt"
)

// ==================== 二进制流读取器 ==================== //

// BinaryReader 自动管理二进制流的读取器
type BinaryReader struct {
	data   []byte
	offset int
}

// 创建新的二进制读取器
func NewBinaryReader(data []byte) *BinaryReader {
	return &BinaryReader{
		data:   data,
		offset: 0,
	}
}

// 验证Token并返回用户信息
func (r *BinaryReader) ValidateToken(authService interface {
	ValidateToken(token string) (interface{}, error)
}) (interface{}, error) {
	// 读取token
	token, err := r.ReadString()
	if err != nil {
		return nil, NewProtocolError(ErrorCodeInvalidRequest, "无法读取token")
	}

	// 验证token并返回用户信息
	user, err := authService.ValidateToken(token)
	if err != nil {
		// 检查是否已经是 ProtocolError，如果是则直接返回
		if protocolErr, ok := err.(*ProtocolError); ok {
			return nil, protocolErr
		}
		// 否则包装为 ErrorCodeUnauthorized
		return nil, NewProtocolError(ErrorCodeUnauthorized, "Token验证失败")
	}

	return user, nil
}

// ReadString 读取字符串（4字节长度前缀 + 内容）
func (r *BinaryReader) ReadString() (string, error) {
	// 读取长度
	length, err := r.ReadUint32()
	if err != nil {
		return "", err
	}

	// 检查剩余数据是否足够
	if r.offset+int(length) > len(r.data) {
		return "", fmt.Errorf("insufficient data for string: need %d bytes, got %d", length, len(r.data)-r.offset)
	}

	// 读取内容
	value := string(r.data[r.offset : r.offset+int(length)])
	r.offset += int(length)
	return value, nil
}

// ReadUint8 读取8位无符号整数
func (r *BinaryReader) ReadUint8() (uint8, error) {
	if r.offset+1 > len(r.data) {
		return 0, fmt.Errorf("insufficient data for uint8: need 1 byte, got %d", len(r.data)-r.offset)
	}
	value := r.data[r.offset]
	r.offset++
	return value, nil
}

// ReadUint16 读取16位无符号整数（小端序）
func (r *BinaryReader) ReadUint16() (uint16, error) {
	if r.offset+2 > len(r.data) {
		return 0, fmt.Errorf("insufficient data for uint16: need 2 bytes, got %d", len(r.data)-r.offset)
	}
	value := binary.LittleEndian.Uint16(r.data[r.offset:])
	r.offset += 2
	return value, nil
}

// ReadUint32 读取32位无符号整数（小端序）
func (r *BinaryReader) ReadUint32() (uint32, error) {
	if r.offset+4 > len(r.data) {
		return 0, fmt.Errorf("insufficient data for uint32: need 4 bytes, got %d", len(r.data)-r.offset)
	}
	value := binary.LittleEndian.Uint32(r.data[r.offset:])
	r.offset += 4
	return value, nil
}

// ReadUint64 读取64位无符号整数（小端序）
func (r *BinaryReader) ReadUint64() (uint64, error) {
	if r.offset+8 > len(r.data) {
		return 0, fmt.Errorf("insufficient data for uint64: need 8 bytes, got %d", len(r.data)-r.offset)
	}
	value := binary.LittleEndian.Uint64(r.data[r.offset:])
	r.offset += 8
	return value, nil
}

// ReadInt8 读取8位有符号整数
func (r *BinaryReader) ReadInt8() (int8, error) {
	if r.offset+1 > len(r.data) {
		return 0, fmt.Errorf("insufficient data for int8: need 1 byte, got %d", len(r.data)-r.offset)
	}
	value := int8(r.data[r.offset])
	r.offset++
	return value, nil
}

// ReadInt16 读取16位有符号整数（小端序）
func (r *BinaryReader) ReadInt16() (int16, error) {
	if r.offset+2 > len(r.data) {
		return 0, fmt.Errorf("insufficient data for int16: need 2 bytes, got %d", len(r.data)-r.offset)
	}
	value := int16(binary.LittleEndian.Uint16(r.data[r.offset:]))
	r.offset += 2
	return value, nil
}

// ReadInt32 读取32位有符号整数（小端序）
func (r *BinaryReader) ReadInt32() (int32, error) {
	if r.offset+4 > len(r.data) {
		return 0, fmt.Errorf("insufficient data for int32: need 4 bytes, got %d", len(r.data)-r.offset)
	}
	value := int32(binary.LittleEndian.Uint32(r.data[r.offset:]))
	r.offset += 4
	return value, nil
}

// ReadInt64 读取64位有符号整数（小端序）
func (r *BinaryReader) ReadInt64() (int64, error) {
	if r.offset+8 > len(r.data) {
		return 0, fmt.Errorf("insufficient data for int64: need 8 bytes, got %d", len(r.data)-r.offset)
	}
	value := int64(binary.LittleEndian.Uint64(r.data[r.offset:]))
	r.offset += 8
	return value, nil
}

// ReadBool 读取布尔值（1字节，1=true, 0=false）
func (r *BinaryReader) ReadBool() (bool, error) {
	if r.offset+1 > len(r.data) {
		return false, fmt.Errorf("insufficient data for bool: need 1 byte, got %d", len(r.data)-r.offset)
	}
	value := r.data[r.offset] != 0
	r.offset++
	return value, nil
}

// ReadBytes 读取指定长度的字节数组
func (r *BinaryReader) ReadBytes(length int) ([]byte, error) {
	if r.offset+length > len(r.data) {
		return nil, fmt.Errorf("insufficient data for bytes: need %d bytes, got %d", length, len(r.data)-r.offset)
	}
	value := make([]byte, length)
	copy(value, r.data[r.offset:r.offset+length])
	r.offset += length
	return value, nil
}

// Position 获取当前读取位置
func (r *BinaryReader) Position() int {
	return r.offset
}

// SetPosition 设置读取位置
func (r *BinaryReader) SetPosition(offset int) error {
	if offset < 0 || offset > len(r.data) {
		return fmt.Errorf("invalid offset: %d, data length: %d", offset, len(r.data))
	}
	r.offset = offset
	return nil
}

// Remaining 获取剩余字节数
func (r *BinaryReader) Remaining() int {
	return len(r.data) - r.offset
}

// ==================== 二进制流存储器 ==================== //

// BinaryWriter 自动管理二进制流的写入器
type BinaryWriter struct {
	buf []byte
}

// NewBinaryWriter 创建新的二进制写入器
func NewBinaryWriter() *BinaryWriter {
	return &BinaryWriter{
		buf: make([]byte, 0, 1024), // 初始容量1KB
	}
}

// WriteString 写入字符串（4字节长度前缀 + 内容）
func (w *BinaryWriter) WriteString(value string) {
	// 写入长度
	w.WriteUint32(uint32(len(value)))
	// 写入内容
	w.buf = append(w.buf, []byte(value)...)
}

// WriteUint8 写入8位无符号整数
func (w *BinaryWriter) WriteUint8(value uint8) {
	w.buf = append(w.buf, value)
}

// WriteByte 写入单个字节（与WriteUint8相同）
func (w *BinaryWriter) WriteByte(value byte) error {
	w.buf = append(w.buf, value)
	return nil
}

// WriteUint16 写入16位无符号整数（小端序）
func (w *BinaryWriter) WriteUint16(value uint16) {
	temp := make([]byte, 2)
	binary.LittleEndian.PutUint16(temp, value)
	w.buf = append(w.buf, temp...)
}

// WriteUint32 写入32位无符号整数（小端序）
func (w *BinaryWriter) WriteUint32(value uint32) {
	temp := make([]byte, 4)
	binary.LittleEndian.PutUint32(temp, value)
	w.buf = append(w.buf, temp...)
}

// WriteUint64 写入64位无符号整数（小端序）
func (w *BinaryWriter) WriteUint64(value uint64) {
	temp := make([]byte, 8)
	binary.LittleEndian.PutUint64(temp, value)
	w.buf = append(w.buf, temp...)
}

// WriteInt8 写入8位有符号整数
func (w *BinaryWriter) WriteInt8(value int8) {
	w.buf = append(w.buf, byte(value))
}

// WriteInt16 写入16位有符号整数（小端序）
func (w *BinaryWriter) WriteInt16(value int16) {
	temp := make([]byte, 2)
	binary.LittleEndian.PutUint16(temp, uint16(value))
	w.buf = append(w.buf, temp...)
}

// WriteInt32 写入32位有符号整数（小端序）
func (w *BinaryWriter) WriteInt32(value int32) {
	temp := make([]byte, 4)
	binary.LittleEndian.PutUint32(temp, uint32(value))
	w.buf = append(w.buf, temp...)
}

// WriteInt64 写入64位有符号整数（小端序）
func (w *BinaryWriter) WriteInt64(value int64) {
	temp := make([]byte, 8)
	binary.LittleEndian.PutUint64(temp, uint64(value))
	w.buf = append(w.buf, temp...)
}

// WriteBool 写入布尔值（1字节，true=1, false=0）
func (w *BinaryWriter) WriteBool(value bool) {
	if value {
		w.buf = append(w.buf, 1)
	} else {
		w.buf = append(w.buf, 0)
	}
}

// WriteBytes 写入字节数组
func (w *BinaryWriter) WriteBytes(value []byte) {
	w.buf = append(w.buf, value...)
}

// Bytes 获取当前缓冲区的字节数组
func (w *BinaryWriter) Bytes() []byte {
	return w.buf
}

// Length 获取当前缓冲区长度
func (w *BinaryWriter) Length() int {
	return len(w.buf)
}

// Reset 重置缓冲区
func (w *BinaryWriter) Reset() {
	w.buf = w.buf[:0]
}
