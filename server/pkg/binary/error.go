package binary

// 错误码定义
const (
	ErrorCodeSuccess          = 0
	ErrorCodeError            = 500  // 非协议错误
	ErrorCodeServerError      = 1000 // 服务器内部错误
	ErrorCodeInvalidRequest   = 1001 // 无效请求
	ErrorCodeUnauthorized     = 1002 // 未授权
	ErrorCodeInvalidTimestamp = 1003 // 时间戳无效（需要更新设备时间）
	ErrorCodeNotFound         = 1004 // 资源不存在
	ErrorCodeRateLimit        = 1005 // 频率限制
	ErrorCodeNoActivity       = 1010 // 暂无活动
	ErrorCodeAlreadySigned    = 1011 // 已签到
)

// 错误码到消息的映射
var ErrorMessages = map[uint32]string{
	ErrorCodeServerError:      "服务器内部错误",
	ErrorCodeInvalidRequest:   "无效请求",
	ErrorCodeUnauthorized:     "未授权",
	ErrorCodeInvalidTimestamp: "无效时间戳",
	ErrorCodeNotFound:         "资源不存在",
	ErrorCodeRateLimit:        "速率限制",
	ErrorCodeNoActivity:       "暂无活动",
	ErrorCodeAlreadySigned:    "已经签到",
}

// 协议错误
type ProtocolError struct {
	Code    uint32
	Message string
}

// 创建新的协议错误
func NewProtocolError(code uint32, message string) error {
	return &ProtocolError{
		Code:    code,
		Message: message,
	}
}

// 实现接口
func (e *ProtocolError) Error() string {
	// 如果定义消息则返回消息
	if e.Message != "" {
		return e.Message
	}

	// 否则使用错误码的固定文本
	if msg, exists := ErrorMessages[e.Code]; exists {
		return msg
	}

	return "未定义错误"
}
