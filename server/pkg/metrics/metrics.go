package metrics

import (
	"sync"
	"time"
)

// Metrics 指标收集器
type Metrics struct {
	mu           sync.RWMutex
	requestCount int64
	errorCount   int64
	responseTime []time.Duration
	cacheHits    int64
	cacheMisses  int64
}

// NewMetrics 创建新的指标收集器
func NewMetrics() *Metrics {
	return &Metrics{
		responseTime: make([]time.Duration, 0),
	}
}

// RecordRequest 记录请求
func (m *Metrics) RecordRequest() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.requestCount++
}

// RecordError 记录错误
func (m *Metrics) RecordError() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.errorCount++
}

// RecordResponseTime 记录响应时间
func (m *Metrics) RecordResponseTime(duration time.Duration) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.responseTime = append(m.responseTime, duration)
}

// RecordCacheHit 记录缓存命中
func (m *Metrics) RecordCacheHit() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.cacheHits++
}

// RecordCacheMiss 记录缓存未命中
func (m *Metrics) RecordCacheMiss() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.cacheMisses++
}

// GetStats 获取统计信息
func (m *Metrics) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var avgResponseTime time.Duration
	if len(m.responseTime) > 0 {
		var total time.Duration
		for _, t := range m.responseTime {
			total += t
		}
		avgResponseTime = total / time.Duration(len(m.responseTime))
	}

	return map[string]interface{}{
		"request_count":     m.requestCount,
		"error_count":       m.errorCount,
		"avg_response_time": avgResponseTime,
		"cache_hits":        m.cacheHits,
		"cache_misses":      m.cacheMisses,
		"cache_hit_ratio":   float64(m.cacheHits) / float64(m.cacheHits+m.cacheMisses),
	}
}
