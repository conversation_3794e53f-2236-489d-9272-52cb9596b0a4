package middleware

import (
	"log"
	"time"
)

// LogMiddleware 日志中间件
type LogMiddleware struct {
	logger *log.Logger
}

// NewLogMiddleware 创建日志中间件
func NewLogMiddleware(logger *log.Logger) *LogMiddleware {
	return &LogMiddleware{
		logger: logger,
	}
}

// LogRequest 记录请求日志
func (m *LogMiddleware) LogRequest(handler func() error) error {
	start := time.Now()
	err := handler()
	duration := time.Since(start)

	if err != nil {
		m.logger.Printf("[ERROR] Request failed after %v: %v", duration, err)
	} else {
		m.logger.Printf("[INFO] Request completed in %v", duration)
	}

	return err
}

// LogOperation 记录操作日志
func (m *LogMiddleware) LogOperation(operation string, handler func() error) error {
	m.logger.Printf("[INFO] Starting operation: %s", operation)
	start := time.Now()
	err := handler()
	duration := time.Since(start)

	if err != nil {
		m.logger.Printf("[ERROR] Operation %s failed after %v: %v", operation, duration, err)
	} else {
		m.logger.Printf("[INFO] Operation %s completed in %v", operation, duration)
	}

	return err
}
