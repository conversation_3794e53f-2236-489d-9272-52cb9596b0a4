package middleware

import (
	"fmt"
	"runtime/debug"
	"udp-server/server/pkg/logger"
)

// RecoverMiddleware 错误恢复中间件
func RecoverMiddleware(next func()) {
	defer func() {
		if r := recover(); r != nil {
			logger.Error("Recovered from panic: %v\nStack trace:\n%s", r, debug.Stack())
		}
	}()
	next()
}

// RecoverWithError 带错误返回的恢复中间件
func RecoverWithError(next func() error) (err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.E<PERSON>rf("recovered from panic: %v", r)
			logger.Error("Panic recovered: %v\nStack trace:\n%s", r, debug.Stack())
		}
	}()
	return next()
}
