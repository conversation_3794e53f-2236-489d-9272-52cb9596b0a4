package utils

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/hex"
	"encoding/pem"
	"fmt"
	"time"
)

// GenerateRSAKeyPair 生成RSA密钥对
func GenerateRSAKeyPair() (*rsa.PrivateKey, *rsa.PublicKey, error) {
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, nil, err
	}
	return privateKey, &privateKey.PublicKey, nil
}

// ExportRSAPrivateKey 导出RSA私钥
func ExportRSAPrivateKey(privateKey *rsa.PrivateKey) string {
	privateKeyBytes := x509.MarshalPKCS1PrivateKey(privateKey)
	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyBytes,
	})
	return string(privateKeyPEM)
}

// ExportRSAPublicKey 导出RSA公钥
func ExportRSAPublicKey(publicKey *rsa.PublicKey) string {
	publicKeyBytes := x509.MarshalPKCS1PublicKey(publicKey)
	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: publicKeyBytes,
	})
	return string(publicKeyPEM)
}

// GenerateToken 生成动态令牌
func GenerateToken(deviceFingerprint string) string {
	// 使用时间戳和设备指纹生成令牌
	timestamp := time.Now().Unix()
	data := fmt.Sprintf("%s|%d", deviceFingerprint, timestamp)
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// ValidateToken 验证令牌
func ValidateToken(token, deviceFingerprint string) bool {
	// 验证令牌是否在有效期内（5分钟）
	timestamp := time.Now().Unix()
	for i := 0; i < 5; i++ {
		checkTime := timestamp - int64(i*60)
		data := fmt.Sprintf("%s|%d", deviceFingerprint, checkTime)
		hash := sha256.Sum256([]byte(data))
		if hex.EncodeToString(hash[:]) == token {
			return true
		}
	}
	return false
}
