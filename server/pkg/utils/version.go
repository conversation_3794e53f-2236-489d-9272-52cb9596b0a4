package utils

import (
	"fmt"
	"strconv"
	"strings"
)

// Version 表示一个版本号
type Version struct {
	parts  []int  // 版本号的各个部分，如 [3, 1, 5, 0]
	suffix string // 版本后缀，如 "beta", "alpha" 等
	raw    string // 原始版本字符串
}

// NewVersion 创建一个新的版本实例
func NewVersion(versionStr string) (*Version, error) {
	if versionStr == "" {
		return nil, fmt.Errorf("version string cannot be empty")
	}

	v := &Version{
		raw: versionStr,
	}

	// 解析版本号
	if err := v.parse(versionStr); err != nil {
		return nil, fmt.Erro<PERSON>("failed to parse version %s: %v", versionStr, err)
	}

	return v, nil
}

// MustNewVersion 创建版本实例，如果失败则 panic
func MustNewVersion(versionStr string) *Version {
	v, err := NewVersion(versionStr)
	if err != nil {
		panic(err)
	}
	return v
}

// parse 解析版本字符串
func (v *Version) parse(versionStr string) error {
	// 查找版本后缀（如 -beta, -alpha, _rc1 等）
	suffixIndex := v.findSuffixIndex(versionStr)
	if suffixIndex != -1 {
		v.suffix = versionStr[suffixIndex+1:] // 跳过分隔符
		versionStr = versionStr[:suffixIndex]
	}

	// 按 . 分割版本号
	parts := strings.Split(versionStr, ".")
	v.parts = make([]int, 0, len(parts))

	for _, part := range parts {
		if part == "" {
			continue // 跳过空部分
		}

		// 只保留数字部分
		numStr := ""
		for _, char := range part {
			if char >= '0' && char <= '9' {
				numStr += string(char)
			} else {
				break // 遇到非数字字符停止
			}
		}

		if numStr == "" {
			continue // 跳过没有数字的部分
		}

		num, err := strconv.Atoi(numStr)
		if err != nil {
			return fmt.Errorf("invalid number in version part %s", part)
		}

		v.parts = append(v.parts, num)
	}

	if len(v.parts) == 0 {
		return fmt.Errorf("no valid version parts found")
	}

	return nil
}

// findSuffixIndex 查找版本后缀的起始位置
func (v *Version) findSuffixIndex(versionStr string) int {
	separators := []string{"-", "_", "+"}
	minIndex := -1

	for _, sep := range separators {
		index := strings.Index(versionStr, sep)
		if index != -1 && (minIndex == -1 || index < minIndex) {
			minIndex = index
		}
	}

	return minIndex
}

// Compare 比较两个版本
// 返回值：-1 表示当前版本小于 other，0 表示相等，1 表示当前版本大于 other
func (v *Version) Compare(other *Version) int {
	if other == nil {
		return 1
	}

	// 比较版本号部分
	maxLen := len(v.parts)
	if len(other.parts) > maxLen {
		maxLen = len(other.parts)
	}

	for i := 0; i < maxLen; i++ {
		vPart := 0
		if i < len(v.parts) {
			vPart = v.parts[i]
		}

		otherPart := 0
		if i < len(other.parts) {
			otherPart = other.parts[i]
		}

		if vPart < otherPart {
			return -1
		} else if vPart > otherPart {
			return 1
		}
	}

	// 版本号部分相同，比较后缀
	return v.compareSuffix(other)
}

// compareSuffix 比较版本后缀
func (v *Version) compareSuffix(other *Version) int {
	// 如果都没有后缀，则相等
	if v.suffix == "" && other.suffix == "" {
		return 0
	}

	// 有后缀的版本小于没有后缀的版本（如 1.0.0-beta < 1.0.0）
	if v.suffix == "" && other.suffix != "" {
		return 1
	}
	if v.suffix != "" && other.suffix == "" {
		return -1
	}

	// 都有后缀，按字典序比较
	if v.suffix < other.suffix {
		return -1
	} else if v.suffix > other.suffix {
		return 1
	}

	return 0
}

// IsLessThan 检查当前版本是否小于 other
func (v *Version) IsLessThan(other *Version) bool {
	return v.Compare(other) < 0
}

// IsGreaterThan 检查当前版本是否大于 other
func (v *Version) IsGreaterThan(other *Version) bool {
	return v.Compare(other) > 0
}

// IsEqual 检查当前版本是否等于 other
func (v *Version) IsEqual(other *Version) bool {
	return v.Compare(other) == 0
}

// IsLessOrEqual 检查当前版本是否小于等于 other
func (v *Version) IsLessOrEqual(other *Version) bool {
	return v.Compare(other) <= 0
}

// IsGreaterOrEqual 检查当前版本是否大于等于 other
func (v *Version) IsGreaterOrEqual(other *Version) bool {
	return v.Compare(other) >= 0
}

// String 返回版本的字符串表示
func (v *Version) String() string {
	return v.raw
}

// Normalized 返回标准化的版本字符串（不包含后缀）
func (v *Version) Normalized() string {
	if len(v.parts) == 0 {
		return "0"
	}

	parts := make([]string, len(v.parts))
	for i, part := range v.parts {
		parts[i] = strconv.Itoa(part)
	}

	return strings.Join(parts, ".")
}

// Parts 返回版本号的各个部分
func (v *Version) Parts() []int {
	result := make([]int, len(v.parts))
	copy(result, v.parts)
	return result
}

// Suffix 返回版本后缀
func (v *Version) Suffix() string {
	return v.suffix
}

// Major 返回主版本号
func (v *Version) Major() int {
	if len(v.parts) > 0 {
		return v.parts[0]
	}
	return 0
}

// Minor 返回次版本号
func (v *Version) Minor() int {
	if len(v.parts) > 1 {
		return v.parts[1]
	}
	return 0
}

// Patch 返回补丁版本号
func (v *Version) Patch() int {
	if len(v.parts) > 2 {
		return v.parts[2]
	}
	return 0
}

// Build 返回构建版本号
func (v *Version) Build() int {
	if len(v.parts) > 3 {
		return v.parts[3]
	}
	return 0
}

// CompareVersions 比较两个版本字符串
// 返回值：-1 表示 v1 < v2，0 表示 v1 == v2，1 表示 v1 > v2
func CompareVersions(v1, v2 string) (int, error) {
	version1, err := NewVersion(v1)
	if err != nil {
		return 0, fmt.Errorf("invalid version v1 %s: %v", v1, err)
	}

	version2, err := NewVersion(v2)
	if err != nil {
		return 0, fmt.Errorf("invalid version v2 %s: %v", v2, err)
	}

	return version1.Compare(version2), nil
}

// IsVersionOutdated 检查客户端版本是否过时（需要更新）
// 如果 clientVersion < serverVersion，返回 true
func IsVersionOutdated(clientVersion, serverVersion string) (bool, error) {
	result, err := CompareVersions(clientVersion, serverVersion)
	if err != nil {
		return false, err
	}
	return result < 0, nil
}
