package utils

import (
	"context"
	"fmt"
	"sync"
	"time"

	"udp-server/server/pkg/logger"
)

// BatchProcessor 批量处理器
type BatchProcessor struct {
	batchSize    int
	flushTimeout time.Duration
	processor    func([]interface{}) error
	buffer       []interface{}
	mu           sync.Mutex
	timer        *time.Timer
	ctx          context.Context
	cancel       context.CancelFunc
	wg           sync.WaitGroup
}

// NewBatchProcessor 创建批量处理器
func NewBatchProcessor(batchSize int, flushTimeout time.Duration, processor func([]interface{}) error) *BatchProcessor {
	ctx, cancel := context.WithCancel(context.Background())

	bp := &BatchProcessor{
		batchSize:    batchSize,
		flushTimeout: flushTimeout,
		processor:    processor,
		buffer:       make([]interface{}, 0, batchSize),
		ctx:          ctx,
		cancel:       cancel,
	}

	bp.timer = time.NewTimer(flushTimeout)
	bp.timer.Stop()

	// 启动定时刷新协程
	bp.wg.Add(1)
	go bp.timerWorker()

	return bp
}

// Add 添加项目到批处理
func (bp *BatchProcessor) Add(item interface{}) error {
	bp.mu.Lock()
	defer bp.mu.Unlock()

	bp.buffer = append(bp.buffer, item)

	// 如果缓冲区为空，启动定时器
	if len(bp.buffer) == 1 {
		bp.timer.Reset(bp.flushTimeout)
	}

	// 如果达到批次大小，立即刷新
	if len(bp.buffer) >= bp.batchSize {
		return bp.flushLocked()
	}

	return nil
}

// Flush 手动刷新
func (bp *BatchProcessor) Flush() error {
	bp.mu.Lock()
	defer bp.mu.Unlock()
	return bp.flushLocked()
}

// flushLocked 内部刷新方法（需要持有锁）
func (bp *BatchProcessor) flushLocked() error {
	if len(bp.buffer) == 0 {
		return nil
	}

	// 停止定时器
	bp.timer.Stop()

	// 复制缓冲区
	items := make([]interface{}, len(bp.buffer))
	copy(items, bp.buffer)

	// 清空缓冲区
	bp.buffer = bp.buffer[:0]

	// 处理批次
	start := time.Now()
	err := bp.processor(items)
	duration := time.Since(start)

	if err != nil {
		logger.Error("Batch process failed: size=%d, duration=%v, error=%v", len(items), duration, err)
	} else {
		logger.Debug("Batch process completed: size=%d, duration=%v", len(items), duration)
	}

	return err
}

// timerWorker 定时器工作协程
func (bp *BatchProcessor) timerWorker() {
	defer bp.wg.Done()

	for {
		select {
		case <-bp.ctx.Done():
			return
		case <-bp.timer.C:
			bp.Flush()
		}
	}
}

// Close 关闭批处理器
func (bp *BatchProcessor) Close() error {
	// 最后刷新一次
	if err := bp.Flush(); err != nil {
		logger.Error("批处理器关闭时刷新失败: %v", err)
	}

	bp.cancel()
	bp.wg.Wait()

	return nil
}

// AsyncBatchProcessor 异步批量处理器
type AsyncBatchProcessor struct {
	*BatchProcessor
	queue   chan interface{}
	workers int
}

// NewAsyncBatchProcessor 创建异步批量处理器
func NewAsyncBatchProcessor(batchSize int, flushTimeout time.Duration, workers int, processor func([]interface{}) error) *AsyncBatchProcessor {
	bp := NewBatchProcessor(batchSize, flushTimeout, processor)

	abp := &AsyncBatchProcessor{
		BatchProcessor: bp,
		queue:          make(chan interface{}, batchSize*workers),
		workers:        workers,
	}

	// 启动工作协程
	for i := 0; i < workers; i++ {
		abp.wg.Add(1)
		go abp.worker()
	}

	return abp
}

// worker 工作协程
func (abp *AsyncBatchProcessor) worker() {
	defer abp.wg.Done()

	for {
		select {
		case <-abp.ctx.Done():
			return
		case item := <-abp.queue:
			if err := abp.BatchProcessor.Add(item); err != nil {
				logger.Error("异步批处理添加失败: %v", err)
			}
		}
	}
}

// AddAsync 异步添加项目
func (abp *AsyncBatchProcessor) AddAsync(item interface{}) error {
	select {
	case abp.queue <- item:
		return nil
	default:
		logger.Error("Async batch queue full: size=%d, capacity=%d", len(abp.queue), cap(abp.queue))
		return fmt.Errorf("async batch queue is full, size: %d, capacity: %d", len(abp.queue), cap(abp.queue))
	}
}

// Close 关闭异步批处理器
func (abp *AsyncBatchProcessor) Close() error {
	close(abp.queue)
	abp.cancel()
	abp.wg.Wait()

	return abp.BatchProcessor.Close()
}

// ConcurrentProcessor 并发处理器
type ConcurrentProcessor struct {
	workers   int
	queue     chan ProcessTask
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
	semaphore chan struct{}
}

// ProcessTask 处理任务
type ProcessTask struct {
	Data     interface{}
	Callback func(interface{}, error)
}

// NewConcurrentProcessor 创建并发处理器
func NewConcurrentProcessor(workers int, queueSize int) *ConcurrentProcessor {
	ctx, cancel := context.WithCancel(context.Background())

	cp := &ConcurrentProcessor{
		workers:   workers,
		queue:     make(chan ProcessTask, queueSize),
		ctx:       ctx,
		cancel:    cancel,
		semaphore: make(chan struct{}, workers),
	}

	// 启动工作协程
	for i := 0; i < workers; i++ {
		cp.wg.Add(1)
		go cp.worker()
	}

	return cp
}

// worker 工作协程
func (cp *ConcurrentProcessor) worker() {
	defer cp.wg.Done()

	for {
		select {
		case <-cp.ctx.Done():
			return
		case task := <-cp.queue:
			cp.processTask(task)
		}
	}
}

// processTask 处理任务
func (cp *ConcurrentProcessor) processTask(task ProcessTask) {
	// 获取信号量
	cp.semaphore <- struct{}{}
	defer func() { <-cp.semaphore }()

	start := time.Now()

	// 这里应该调用实际的处理函数
	// 为了示例，我们只是记录日志
	duration := time.Since(start)

	logger.Debug("Concurrent process completed: type=%s, duration=%v", getTypeName(task.Data), duration)

	if task.Callback != nil {
		task.Callback(task.Data, nil)
	}
}

// Submit 提交任务
func (cp *ConcurrentProcessor) Submit(data interface{}, callback func(interface{}, error)) error {
	task := ProcessTask{
		Data:     data,
		Callback: callback,
	}

	select {
	case cp.queue <- task:
		return nil
	default:
		logger.Error("Concurrent processor queue full: size=%d, capacity=%d", len(cp.queue), cap(cp.queue))
		return fmt.Errorf("concurrent processor queue is full, size: %d, capacity: %d", len(cp.queue), cap(cp.queue))
	}
}

// Close 关闭并发处理器
func (cp *ConcurrentProcessor) Close() error {
	cp.cancel()
	close(cp.queue)
	cp.wg.Wait()
	return nil
}

// getTypeName 获取类型名称
func getTypeName(v interface{}) string {
	if v == nil {
		return "nil"
	}
	return fmt.Sprintf("%T", v)
}

// RateLimiter 速率限制器
type RateLimiter struct {
	rate     int
	interval time.Duration
	tokens   chan struct{}
	ticker   *time.Ticker
	ctx      context.Context
	cancel   context.CancelFunc
}

// NewRateLimiter 创建速率限制器
func NewRateLimiter(rate int, interval time.Duration) *RateLimiter {
	ctx, cancel := context.WithCancel(context.Background())

	rl := &RateLimiter{
		rate:     rate,
		interval: interval,
		tokens:   make(chan struct{}, rate),
		ticker:   time.NewTicker(interval),
		ctx:      ctx,
		cancel:   cancel,
	}

	// 初始化令牌
	for i := 0; i < rate; i++ {
		rl.tokens <- struct{}{}
	}

	// 启动令牌补充协程
	go rl.refillTokens()

	return rl
}

// refillTokens 补充令牌
func (rl *RateLimiter) refillTokens() {
	for {
		select {
		case <-rl.ctx.Done():
			return
		case <-rl.ticker.C:
			// 补充令牌到满额
			for len(rl.tokens) < rl.rate {
				select {
				case rl.tokens <- struct{}{}:
				default:
					break
				}
			}
		}
	}
}

// Allow 检查是否允许操作
func (rl *RateLimiter) Allow() bool {
	select {
	case <-rl.tokens:
		return true
	default:
		return false
	}
}

// Wait 等待直到允许操作
func (rl *RateLimiter) Wait(ctx context.Context) error {
	select {
	case <-rl.tokens:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	case <-rl.ctx.Done():
		return rl.ctx.Err()
	}
}

// Close 关闭速率限制器
func (rl *RateLimiter) Close() {
	rl.cancel()
	rl.ticker.Stop()
}
