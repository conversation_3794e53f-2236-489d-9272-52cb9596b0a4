package main

import (
	"fmt"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"time"
	"udp-server/server/internal/database"
	gatewayHandler "udp-server/server/internal/gateway/handler"
	"udp-server/server/internal/gateway/middleware"
	gatewayService "udp-server/server/internal/gateway/service"
	"udp-server/server/internal/gateway/view"
	"udp-server/server/internal/handler"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"
	"udp-server/server/pkg/metrics"

	"github.com/gorilla/mux"
	"github.com/spf13/viper"
)

func main() {
	// 初始化基础组件
	initConfig()
	initDatabase()
	metricsInstance := metrics.NewMetrics()
	initLogger()
	defer logger.Close()

	logger.Info("服务器启动中...")

	// 初始化缓存服务
	cacheInstance, hashCacheService := initCacheServices()

	// 创建服务实例
	permissionService := createPermissionService(cacheInstance, hashCacheService)
	riskService := service.NewRiskControlService(database.GetDB(), cacheInstance)
	authService := createAuthService(cacheInstance, permissionService, riskService)
	heartbeatService := createHeartbeatService(metricsInstance, cacheInstance)
	luckyService := createLuckyService(cacheInstance, permissionService, hashCacheService)
	cdkeyService := service.NewCDKeyService(cacheInstance, permissionService)
	statsService := service.NewStatsService(database.GetDB(), cacheInstance, authService, heartbeatService, riskService)
	announcementService := service.NewAnnouncementService(database.GetDB(), cacheInstance)
	updateService := service.NewUpdateService(database.GetDB(), cacheInstance)
	teamService := service.NewTeamService(database.GetDB(), cacheInstance)
	activityService := service.NewActivityService(database.GetDB(), cacheInstance)

	// 创建安全验证器
	securityValidator := binary.NewSecurityValidator(cacheInstance)

	// 创建处理器实例
	authHandler := handler.NewAuthHandler(authService, permissionService, riskService, updateService)
	heartbeatHandler := handler.NewHeartbeatHandler(authService, heartbeatService, updateService, permissionService, announcementService)
	luckyHandler := handler.NewLuckyHandler(luckyService, authService, permissionService, cdkeyService)
	announcementHandler := handler.NewAnnouncementHandler(announcementService, authService)
	updateHandler := handler.NewUpdateHandler(updateService)
	teamHandler := handler.NewTeamHandler(teamService)
	activityHandler := handler.NewActivityHandler(activityService, authService)

	// 创建Gateway服务和处理器
	adminService := gatewayService.NewAdminService(database.GetDB(), cacheInstance)
	riskAdminService := gatewayService.NewRiskControlAdminService(database.GetDB(), riskService)
	adminPermissionService := gatewayService.NewAdminPermissionService(database.GetDB(), permissionService)
	updateAdminService := gatewayService.NewUpdateAdminService(database.GetDB(), cacheInstance)

	adminHandler := gatewayHandler.NewAdminHandler(announcementService, authService, updateService, updateAdminService, statsService, riskService, luckyService, cdkeyService, heartbeatService, permissionService, adminService, adminPermissionService, riskAdminService, activityService, cacheInstance)
	adminView := view.NewAdminView()
	adminAuth := middleware.NewAdminAuthMiddleware(authService, adminPermissionService, adminService)

	// 初始化权限项表
	if err := adminPermissionService.InitializeAdminItems(); err != nil {
		logger.Error("初始化权限项表失败: %v", err)
	}

	// 初始化更新配置缓存
	if err := updateService.InitializeCache(); err != nil {
		logger.Error("Failed to initialize update config cache: %v", err)
	}

	// 启动统计数据收集器和清理任务
	statsService.StartStatsCollector()
	statsService.StartStatsCleanup()

	// 立即保存一次统计数据用于测试
	if err := statsService.SaveCurrentStats(); err != nil {
		logger.Warn("初始统计数据保存失败: %v", err)
	}

	// 启动HTTP服务器（用于管理后台）
	go startHTTPServer(adminHandler, adminView, adminAuth)

	// 启动UDP服务器
	startUDPServer(securityValidator, authHandler, heartbeatHandler, luckyHandler, announcementHandler, updateHandler, teamHandler, activityHandler, permissionService, riskService, updateService)
}

// 初始化配置
func initConfig() {
	// 设置配置文件路径
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("config")
	viper.AddConfigPath(".")

	// 读取环境变量
	viper.AutomaticEnv()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// 配置文件不存在，创建默认配置
			fmt.Printf("配置文件不存在，创建默认配置...\n")
			createDefaultConfig()
		} else {
			fmt.Printf("读取配置文件失败: %v\n", err)
			os.Exit(1)
		}
	}
}

// 创建默认配置文件
func createDefaultConfig() {
	// 确保配置目录存在
	if err := os.MkdirAll("config", 0755); err != nil {
		fmt.Printf("Error creating config directory: %v\n", err)
		os.Exit(1)
	}

	// 创建默认配置
	viper.SetDefault("database.driver", "mysql")
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 3306)
	viper.SetDefault("database.username", "root")
	viper.SetDefault("database.password", "root")
	viper.SetDefault("database.dbname", "bns")
	viper.SetDefault("database.charset", "utf8mb4")
	viper.SetDefault("database.parseTime", true)
	viper.SetDefault("database.loc", "Local")
	viper.SetDefault("database.maxIdleConns", 10)
	viper.SetDefault("database.maxOpenConns", 100)
	viper.SetDefault("database.connMaxLifetime", 3600)

	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8081)

	// HTTP服务器配置
	viper.SetDefault("http.host", "0.0.0.0")
	viper.SetDefault("http.port", 8080)

	// Redis 配置
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 4)
	viper.SetDefault("redis.test_db", 5) // 测试服务器使用不同的数据库ID

	// 日志配置
	viper.SetDefault("log.level", "INFO")
	viper.SetDefault("log.console", true)
	viper.SetDefault("log.file", false)
	viper.SetDefault("log.file_path", "logs/server.log")

	// 心跳配置
	viper.SetDefault("heartbeat.client_interval", "15m")
	viper.SetDefault("heartbeat.server_timeout", "20m")
	viper.SetDefault("heartbeat.cleanup_interval", "5m")

	// 测试服配置
	viper.SetDefault("test_server.enabled", false)

	// 保存配置文件
	if err := viper.WriteConfigAs(filepath.Join("config", "config.yaml")); err != nil {
		fmt.Printf("Error writing config file: %v\n", err)
		os.Exit(1)
	}
}

// 初始化数据库
func initDatabase() {
	if err := database.InitDB(); err != nil {
		fmt.Printf("[Fatal] 连接数据库失败: %v\n", err)
		os.Exit(1)
	}
}

// 初始化日志系统
func initLogger() {
	logConfig := &logger.Config{
		Level:    viper.GetString("log.level"),
		Console:  viper.GetBool("log.console"),
		File:     viper.GetBool("log.file"),
		FilePath: viper.GetString("log.file_path"),
	}
	if err := logger.Init(logConfig); err != nil {
		fmt.Printf("[Fatal] 初始化日志系统失败: %v\n", err)
		os.Exit(1)
	}
}

// 初始化缓存服务
func initCacheServices() (cache.Cache, *service.RedisCache) {
	// Redis配置
	redisHost := viper.GetString("redis.host")
	redisPort := viper.GetInt("redis.port")
	redisPassword := viper.GetString("redis.password")

	// 根据是否为测试服务器选择不同的数据库ID
	var redisDB int
	if viper.GetBool("test_server.enabled") {
		redisDB = viper.GetInt("redis.test_db")
		logger.Info("测试服务器模式，使用Redis数据库 %d", redisDB)
	} else {
		redisDB = viper.GetInt("redis.db")
		logger.Info("正式服务器模式，使用Redis数据库 %d", redisDB)
	}

	logger.Info("正在连接Redis缓存服务器 %s:%d (DB: %d)...", redisHost, redisPort, redisDB)

	// 创建Redis缓存实例
	cacheInstance, err := cache.NewRedisCache(redisHost, redisPort, redisPassword, redisDB)
	if err != nil {
		logger.Error("Redis缓存服务器连接失败: %v", err)
		logger.Error("请检查以下配置:")
		logger.Error("  - Redis服务器地址: %s:%d", redisHost, redisPort)
		logger.Error("  - Redis数据库: %d (测试服务器: %v)", redisDB, viper.GetBool("test_server.enabled"))
		logger.Error("  - Redis服务是否正常运行")
		logger.Error("  - 网络连接是否正常")
		if redisPassword != "" {
			logger.Error("  - Redis密码是否正确")
		}
		logger.Error("服务器启动失败")
		os.Exit(1)
	}

	// 执行Redis功能测试
	logger.Info("正在测试Redis缓存功能...")
	testKey := fmt.Sprintf("server_startup_test_%d", time.Now().Unix())
	testValue := "startup_test_value"

	// 测试写入
	if err := cacheInstance.Set(testKey, testValue, 30*time.Second); err != nil {
		logger.Error("Redis缓存写入测试失败: %v", err)
		logger.Error("服务器启动失败，请检查Redis服务权限配置")
		os.Exit(1)
	}

	// 测试读取
	var readValue string
	if err := cacheInstance.Get(testKey, &readValue); err != nil {
		logger.Error("Redis缓存读取测试失败: %v", err)
		logger.Error("服务器启动失败，请检查Redis服务配置")
		os.Exit(1)
	}

	// 验证数据一致性
	if readValue != testValue {
		logger.Error("Redis缓存数据一致性测试失败: 期望 '%s', 实际 '%s'", testValue, readValue)
		logger.Error("服务器启动失败，请检查Redis服务配置")
		os.Exit(1)
	}

	// 测试删除
	if err := cacheInstance.Delete(testKey); err != nil {
		logger.Error("Redis缓存删除测试失败: %v", err)
		logger.Error("服务器启动失败，请检查Redis服务权限配置")
		os.Exit(1)
	}

	// 创建支持哈希操作的缓存服务实例
	redisAddr := fmt.Sprintf("%s:%d", redisHost, redisPort)
	hashCacheService, err := service.NewRedisCache(redisAddr, redisPassword, redisDB, metrics.NewMetrics())
	if err != nil {
		logger.Error("创建Redis Hash缓存服务失败: %v", err)
		logger.Error("服务器启动失败")
		os.Exit(1)
	}

	// 启动Redis健康检查
	if err := cacheInstance.StartHealthMonitor(30 * time.Second); err != nil {
		logger.Warn("启动Redis健康监控失败: %v", err)
	}

	logger.Info("成功连接到Redis缓存服务器")
	return cacheInstance, hashCacheService
}

// 创建权限服务
func createPermissionService(cacheInstance cache.Cache, hashCacheService *service.RedisCache) *service.PermissionService {
	return service.NewPermissionService(database.GetDB(), cacheInstance, hashCacheService)
}

// 创建认证服务
func createAuthService(cacheInstance cache.Cache, permissionService *service.PermissionService, riskService *service.RiskControlService) *service.AuthService {
	authService := service.NewAuthService(database.GetDB(), cacheInstance, riskService)
	authService.SetPermissionService(permissionService)
	return authService
}

// 创建心跳服务
func createHeartbeatService(metricsInstance *metrics.Metrics, cacheInstance cache.Cache) *service.HeartbeatService {
	// 从viper获取心跳配置
	serverTimeout := viper.GetString("heartbeat.server_timeout")
	cleanupInterval := viper.GetString("heartbeat.cleanup_interval")

	// 解析时间间隔
	serverTimeoutDuration, err := time.ParseDuration(serverTimeout)
	if err != nil {
		logger.Fatal("解析服务器超时时间失败: %v", err)
	}

	cleanupIntervalDuration, err := time.ParseDuration(cleanupInterval)
	if err != nil {
		logger.Fatal("解析清理间隔时间失败: %v", err)
	}

	return service.NewHeartbeatService(serverTimeoutDuration, cleanupIntervalDuration, metricsInstance, cacheInstance)
}

// 创建抽奖服务
func createLuckyService(cacheInstance cache.Cache, permissionService *service.PermissionService, hashCacheService *service.RedisCache) *service.LuckyService {
	return service.NewLuckyService(cacheInstance, permissionService, hashCacheService)
}

// 启动UDP服务器
func startUDPServer(securityValidator *binary.SecurityValidator, authHandler *handler.AuthHandler, heartbeatHandler *handler.HeartbeatHandler, luckyHandler *handler.LuckyHandler, announcementHandler *handler.AnnouncementHandler, updateHandler *handler.UpdateHandler, teamHandler *handler.TeamHandler, activityHandler *handler.ActivityHandler, permissionService *service.PermissionService, riskService *service.RiskControlService, updateService *service.UpdateService) {
	// 创建UDP服务器
	addr := net.UDPAddr{
		Port: viper.GetInt("server.port"),
		IP:   net.ParseIP(viper.GetString("server.host")),
	}

	conn, err := net.ListenUDP("udp", &addr)
	if err != nil {
		logger.Fatal("Failed to start UDP server: %v", err)
	}
	defer conn.Close()

	logger.Info("UDP server started on %s:%d", viper.GetString("server.host"), viper.GetInt("server.port"))

	// 处理消息
	buffer := make([]byte, 65536)
	for {
		n, remoteAddr, err := conn.ReadFromUDP(buffer)
		if err != nil {
			logger.Error("Error reading from UDP: %v", err)
			continue
		}

		// 处理二进制协议消息（包含解密和解压缩）
		msg, parseErr := binary.DecodeMessageWithDecryption(buffer[:n])
		if parseErr != nil {
			logger.Error("Error parsing binary message from %s: %v", remoteAddr.IP.String(), parseErr)
			continue
		}

		// 如果消息还需要解压缩，进行解压缩处理
		if msg.Header.HasFlag(binary.FlagCompressed) {
			msg, parseErr = binary.DecompressMessage(msg)
			if parseErr != nil {
				logger.Error("Error decompressing message from %s: %v", remoteAddr.IP.String(), parseErr)
				continue
			}
		}

		// 根据消息类型处理
		var response *binary.BinaryWriter
		var handleErr error

		switch msg.Header.MsgType {
		case binary.MsgTypeLogin:
			response, handleErr = authHandler.HandleLoginRequest(msg, remoteAddr)
		case binary.MsgTypeHeartbeat:
			response, handleErr = heartbeatHandler.HandleHeartbeatRequest(msg)
		case binary.MsgTypeLogout:
			response, handleErr = authHandler.HandleLogoutRequest(msg, remoteAddr)
		case binary.MsgTypeLuckyDraw:
			response, handleErr = luckyHandler.HandleLuckyDrawRequest(msg, remoteAddr)
		case binary.MsgTypeLuckyStatus:
			response, handleErr = luckyHandler.HandleLuckyStatusRequest(msg, remoteAddr)
		case binary.MsgTypeCDKeyActivate:
			response, handleErr = luckyHandler.HandleCDKeyActivateRequest(msg, remoteAddr)
		case binary.MsgTypeAnnouncementVersion:
			response, handleErr = announcementHandler.HandleAnnouncementVersionRequest(msg, remoteAddr)
		case binary.MsgTypeAnnouncementIds:
			response, handleErr = announcementHandler.HandleAnnouncementIdsRequest(msg, remoteAddr)
		case binary.MsgTypeAnnouncementDetail:
			response, handleErr = announcementHandler.HandleAnnouncementDetailRequest(msg, remoteAddr)
		case binary.MsgTypeUpdateConfig:
			response, handleErr = updateHandler.HandleUpdateConfigRequest(msg, remoteAddr)
		case binary.MsgTypeGetTeamInfo:
			response, handleErr = teamHandler.HandleGetTeamInfoRequest(remoteAddr)
		case binary.MsgTypeActivityList:
			response, handleErr = activityHandler.HandleGetActivityListRequest(msg, remoteAddr)
		case binary.MsgTypeActivityVersion:
			response, handleErr = activityHandler.HandleActivityVersionRequest(msg, remoteAddr)
		case binary.MsgTypeActivityDetail:
			response, handleErr = activityHandler.HandleActivityDetailRequest(msg, remoteAddr)
		default:
			// 未定义的类型忽略响应
			logger.Warn("未定义消息类型: 0x%02X from %s", msg.Header.MsgType, remoteAddr.IP.String())
			continue
		}

		// 创建通用错误响应
		if handleErr != nil {
			logger.Error("Error handling message: %v", handleErr)

			response = binary.NewBinaryWriter()
			// 检查是否是协议错误，如果是则使用其错误码
			if protocolErr, ok := handleErr.(*binary.ProtocolError); ok {
				response.WriteUint32(protocolErr.Code)
			} else {
				// 返回错误信息
				response.WriteUint32(binary.ErrorCodeError)
				response.WriteString(handleErr.Error())
			}
		}

		// 统一数据响应处理
		if response != nil {
			responseType := binary.GetResponseType(msg.Header.MsgType)
			msg := binary.NewMessage(responseType, 0, response.Bytes())
			_, err = conn.WriteToUDP(msg.EncodeWithEncryption(), remoteAddr)
			if err != nil {
				logger.Error("Error sending response: %v", err)
			}
		}
	}
}

// 启动管理后台
func startHTTPServer(h *gatewayHandler.AdminHandler, adminView *view.AdminView, adminAuth *middleware.AdminAuthMiddleware) {
	logger.Info("正在启动HTTP服务器...")

	// 创建路由器
	router := mux.NewRouter()

	// 添加中间件
	router.Use(loggingMiddleware)
	router.Use(corsMiddleware)

	// 管理后台路由（需要认证）
	adminRouter := router.PathPrefix("/admin").Subrouter()
	adminRouter.Use(adminAuth.RequireAuth)

	// 管理后台页面路由
	adminRouter.HandleFunc("", adminView.HandleAdminPage).Methods("GET")
	adminRouter.HandleFunc("/", adminView.HandleAdminPage).Methods("GET")
	adminRouter.HandleFunc("/login", adminView.HandleLoginPage).Methods("GET")
	adminRouter.HandleFunc("/activities", adminView.HandleActivityPage).Methods("GET")

	// 管理后台API路由
	adminAPI := adminRouter.PathPrefix("/api").Subrouter()
	adminAPI.HandleFunc("/login", h.AuthHandler.HandleLogin).Methods("POST")
	adminAPI.HandleFunc("/logout", h.AuthHandler.HandleLogout).Methods("POST")
	adminAPI.HandleFunc("/current", h.HandleGetCurrentAdmin).Methods("GET")
	adminAPI.HandleFunc("/dashboard", h.StatsHandler.HandleDashboard).Methods("GET")
	// 公告管理API（需要相应权限）
	adminAPI.Handle("/announcements", adminAuth.RequirePermission("announcement.view")(http.HandlerFunc(h.AnnouncementHandler.HandleGetAnnouncements))).Methods("GET")
	adminAPI.Handle("/announcements", adminAuth.RequirePermission("announcement.create")(http.HandlerFunc(h.AnnouncementHandler.HandleCreateAnnouncement))).Methods("POST")
	adminAPI.Handle("/announcements/{id}", adminAuth.RequirePermission("announcement.view")(http.HandlerFunc(h.AnnouncementHandler.HandleGetAnnouncement))).Methods("GET")
	adminAPI.Handle("/announcements/{id}", adminAuth.RequirePermission("announcement.edit")(http.HandlerFunc(h.AnnouncementHandler.HandleUpdateAnnouncement))).Methods("PUT", "POST")
	adminAPI.Handle("/announcements/{id}", adminAuth.RequirePermission("announcement.delete")(http.HandlerFunc(h.AnnouncementHandler.HandleDeleteAnnouncement))).Methods("DELETE")
	// 用户管理API（需要相应权限）
	adminAPI.Handle("/users", adminAuth.RequirePermission("user.view")(http.HandlerFunc(h.UserHandler.HandleGetUsers))).Methods("GET")
	adminAPI.Handle("/users/cdkeys", adminAuth.RequirePermission("user.view")(http.HandlerFunc(h.UserHandler.HandleGetUserCDKeys))).Methods("GET")
	adminAPI.Handle("/users/bind-cdkey", adminAuth.RequirePermission("cdkey.create")(http.HandlerFunc(h.CdkeyHandler.HandleBindCDKeyToUser))).Methods("POST")
	adminAPI.Handle("/users/{qq}", adminAuth.RequirePermission("user.view")(http.HandlerFunc(h.UserHandler.HandleGetUser))).Methods("GET")
	adminAPI.Handle("/users/{qq}/status", adminAuth.RequirePermission("user.edit")(http.HandlerFunc(h.UserHandler.HandleUpdateUserStatus))).Methods("PUT")
	adminAPI.Handle("/users/{qq}/kick", adminAuth.RequirePermission("user.edit")(http.HandlerFunc(h.UserHandler.HandleKickUser))).Methods("POST")
	// 口令码管理API（需要相应权限）
	adminAPI.Handle("/cdkeys", adminAuth.RequirePermission("cdkey.view")(http.HandlerFunc(h.CdkeyHandler.HandleGetCDKeys))).Methods("GET")
	adminAPI.Handle("/cdkeys", adminAuth.RequirePermission("cdkey.create")(http.HandlerFunc(h.CdkeyHandler.HandleCreateCDKeys))).Methods("POST")
	adminAPI.Handle("/cdkeys/usage", adminAuth.RequirePermission("cdkey.view")(http.HandlerFunc(h.CdkeyHandler.HandleGetCDKeyUsage))).Methods("GET")
	adminAPI.HandleFunc("/risk/events", h.RiskHandler.HandleGetRiskEvents).Methods("GET")
	adminAPI.HandleFunc("/risk/events/{id}", h.RiskHandler.HandleProcessRiskEvent).Methods("POST")
	adminAPI.HandleFunc("/risk/config", h.RiskHandler.HandleGetRiskConfig).Methods("GET")
	adminAPI.HandleFunc("/risk/config", h.RiskHandler.HandleUpdateRiskConfig).Methods("POST", "PUT")

	// 更具体的路由需要在更通用的路由之前注册
	adminAPI.HandleFunc("/updates", h.UpdateHandler.HandleGetUpdateConfigs).Methods("GET")
	adminAPI.HandleFunc("/updates", h.UpdateHandler.HandleCreateUpdateConfig).Methods("POST")
	adminAPI.HandleFunc("/updates/{id}", h.UpdateHandler.HandleGetUpdateConfig).Methods("GET")
	adminAPI.HandleFunc("/updates/{id}", h.UpdateHandler.HandleUpdateConfig).Methods("POST", "PUT")

	adminAPI.HandleFunc("/stats", h.StatsHandler.HandleGetStats).Methods("GET")
	adminAPI.HandleFunc("/stats/history", h.StatsHandler.HandleGetHistoryStats).Methods("GET")

	// 管理员管理API（需要管理员权限）
	adminAPI.Handle("/admins", adminAuth.RequirePermission("admin.view")(http.HandlerFunc(h.HandleGetAdmins))).Methods("GET")
	adminAPI.Handle("/admins", adminAuth.RequirePermission("admin.create")(http.HandlerFunc(h.HandleCreateAdmin))).Methods("POST")
	adminAPI.Handle("/admins/{id}", adminAuth.RequirePermission("admin.view")(http.HandlerFunc(h.HandleGetAdmin))).Methods("GET")
	adminAPI.Handle("/admins/{id}", adminAuth.RequirePermission("admin.edit")(http.HandlerFunc(h.HandleUpdateAdmin))).Methods("PUT", "POST")
	adminAPI.Handle("/admins/{id}", adminAuth.RequirePermission("admin.delete")(http.HandlerFunc(h.HandleDeleteAdmin))).Methods("DELETE")

	// 权限管理API（需要管理员权限）
	adminAPI.Handle("/admins/{id}/permissions", adminAuth.RequirePermission("admin.view")(http.HandlerFunc(h.HandleGetAdminPermissions))).Methods("GET")
	adminAPI.Handle("/admins/{id}/permissions", adminAuth.RequirePermission("admin.edit")(http.HandlerFunc(h.HandleUpdateAdminPermissions))).Methods("PUT", "POST")

	// 活动管理API
	adminAPI.HandleFunc("/activities", h.ActivityHandler.GetActivityList).Methods("GET")
	adminAPI.HandleFunc("/activities", h.ActivityHandler.CreateActivity).Methods("POST")
	adminAPI.HandleFunc("/activities/{id}", h.ActivityHandler.GetActivityById).Methods("GET")
	adminAPI.HandleFunc("/activities/{id}", h.ActivityHandler.UpdateActivity).Methods("PUT", "POST")
	adminAPI.HandleFunc("/activities/{id}", h.ActivityHandler.DeleteActivity).Methods("DELETE")

	// 流程管理API
	adminAPI.HandleFunc("/flows", h.ActivityHandler.CreateFlow).Methods("POST")
	adminAPI.HandleFunc("/flows", h.ActivityHandler.GetFlowsByActivityId).Methods("GET")
	adminAPI.HandleFunc("/flows/{id}", h.ActivityHandler.UpdateFlow).Methods("PUT", "POST")
	adminAPI.HandleFunc("/flows/{id}", h.ActivityHandler.DeleteFlow).Methods("DELETE")
	adminAPI.HandleFunc("/flows/{id}/test", h.ActivityHandler.TestFlow).Methods("POST")

	// AMS解析API
	adminAPI.HandleFunc("/ams/parse", h.ActivityHandler.ParseAMS).Methods("GET")
	adminAPI.HandleFunc("/ams/create", h.ActivityHandler.CreateActivityAndFlows).Methods("POST")

	// 静态文件路由
	router.PathPrefix("/static/").Handler(http.StripPrefix("/static/", http.HandlerFunc(adminView.HandleStaticFiles)))

	// 获取HTTP服务器配置
	httpHost := viper.GetString("http.host")
	httpPort := viper.GetInt("http.port")

	if httpHost == "" {
		httpHost = "0.0.0.0"
	}
	if httpPort == 0 {
		httpPort = 8080
	}

	addr := fmt.Sprintf("%s:%d", httpHost, httpPort)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         addr,
		Handler:      router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	logger.Info("HTTP服务器启动成功，监听地址: %s", addr)
	logger.Info("可用端点:")
	logger.Info("  GET  /health - 健康检查")
	logger.Info("  GET  /admin - 管理后台主页")
	logger.Info("  GET  /admin/login - 管理后台登录")
	logger.Info("  API  /admin/api/* - 管理后台API接口")

	// 启动服务器
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		logger.Error("HTTP服务器启动失败: %v", err)
	}
}

// HTTP请求日志中间件
func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// 记录请求
		logger.Debug("HTTP请求: %s %s 来自 %s", r.Method, r.URL.Path, r.RemoteAddr)

		// 处理请求
		next.ServeHTTP(w, r)

		// 记录响应时间
		duration := time.Since(start)
		logger.Debug("HTTP响应: %s %s 耗时 %v", r.Method, r.URL.Path, duration)
	})
}

// CORS中间件
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 设置CORS头
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		// 处理预检请求
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}
